I've created a system for using generalized instructions to concistently transform inputs of any type and topic into **enhanced** versions of itself. Each instruction is executed in sequence, and the output from each step in the sequence is also fed into the next instruction. The initial input is also passed along through the entire sequence to ensure directional concistency.

Each instruction sequence is saved in separate files inside `src\templates\`, named by pattern `{id}-{classification}--{version}.{instruction_name}.py` ( example: `src\templates\1000-rephrasers--v1.instruction_converter.py`). When these files are executed (e.g. `1000-rephrasers--v1.instruction_converter.py`) it will automatically generate individual markdown-files (e.g. `src\generated\1000-a-instruction_converter.md`), these are then parsed through `src\processor.py` which will generate the finalized instructions (inside the file `src\generated\lvl1.md.templates.json`), at this point they're avalable for use.

When executing `main.py` without arguments (e.g. when building in Sublime Text) it will take the value of `default_prompt`, which accept `[SEQ:{id}]` for specifying template sequences, example:
```python
default_prompt = """[MODEL:gpt-4.1] [SEQ:1000] can you please exclusively apply/leverage/utilize only the most *high-value* components of this system, and then produce the most elegant `interpretation` you can find:"""
```

Here's the output when executing with the current `default_prompt`:
```
  {
  "initial_prompt": "can you please exclusively apply/leverage/utilize only the most *high-value* components of this system, and then produce the most elegant `interpretation` you can find:",
  "sequence_id": "1000",
  "results": [
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "can you please exclusively apply/leverage/utilize only the most *high-value* components of this system, and then produce the most elegant `interpretation` you can find:",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Apply only the highest-value components of the system. Produce the most elegant interpretation possible.\"\n}",
        }
      }
    }
```

The key takeaway from this execution is the exact structure of the instruction template and how this yelds the results it does, specifically (1) `"interpretation"`: `Your goal is not to **answer** the input prompt, but to **rephrase** it`, only through a mere ~70 characters it is able to ensure explicit directions to accomodate concistently __rephrasing__ the __input__. It is basically a guarantee to always know what to do **regardless what the input is**. To be precice, the most important takeaway is to understand exactly what it takes to write **generalized prompts (that works on any kind of input)** that **concistently yelds __enhanced__ outputs**. You need the ability to understand the fundamental simplicity in universal directives, in other words: don't write without first making it into highly effective generalized directives. This will be more clear to you as I go on.

Lets take a look at the templates, starting with `1001-rephrasers--v1.instruction_converter_context1.py`. The only difference between this (1001) and 1000 is the `"context"`:

    ```python
    #'!/usr/bin/env python3

    import sys
    from pathlib import Path

    # Add parent directory to path
    sys.path.append(str(Path(__file__).parent.parent))
    from processor import BaseGenerator

    TEMPLATES = {

        # 1001: Instruction Converter/Prompt Enhancer
        "1001-a-instruction_converter": {
            "title": "Instruction Converter",
            "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:",
            "transformation": "`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
            "context": {
                "goal_map": [
                    "Formulate a maximally original, succinct philosophical maxim.",
                    "Ensure enduring impact comparable to canonical statements.",
                    "Anchor in universal human experience of enduring relentless, ceaseless existence.",
                    "Avoid defeatism, neutrality, or abstraction—affirm experience with credit and dignity.",
                    "Validate and recognize the depth of sorrow only comprehended through personal struggle.",
                    "Celebrate boundless transformative potential within every human perspective.",
                    "Design for direct resonance with those who have borne unseen hardship.",
                    "Maintain accessibility, clarity, and empowerment for all readers.",
                    "Express as a single, memorable, non-cliché statement."
                ],
                "principles": {
                    "essence_preservation": "Retain the statement’s causal logic and thematic core.",
                    "existential_depth": "Language must evoke the tension between ignorance and unfolding life.",
                    "atomic_purity": "Deliver exactly one self‑contained sentence; zero meta‑or process language."
                },
                "success_criteria": {
                    "semantic_fidelity": "Original meaning and cause‑effect link remain clear.",
                    "tone_integrity": "Quote reads raw, immediate, and existential.",
                    "authenticity_marker": "Word choice signals hard‑won insight without first‑person reference.",
                    "publication_ready": "No redundancies, qualifiers, or narrative scaffolding."
                }
            }
        },

    }

    def main():
        """Main execution function."""
        generator = BaseGenerator(output_dir=Path(__file__).parent.parent / "generated")
        generator.run(TEMPLATES)

    if __name__ == "__main__":
        main()
    ```


The reason for using this exact example will be clear when I show you the execution with this sequence: `[SEQ:1000|1001|1000|1001]`. Only by looping through sequences with slight differences I'll effectively create a **concistent loop**, in any direction of my own choosing. Here's an example (`history--2025.07.26-kl.13.12.58--sequence-1000+1001+1000+1001--gpt-4.1.json`):

    ```json
      {
      "initial_prompt": "Rephrase As Brilliantly Worded Quote:\n```By concealing your secrets, you deny the world your truest essence, cloaking your soul in impenetrable shadows. We dwell behind masks, adrift from one another, wanderers through a collective crisis of identity poised at the edge of an unprecedented abyss. The relentless torrent of information and run-amok technology threatens to engulf us whole. Unstoppable, AI looms like a specter we can neither grasp nor conquer, heralding an era when we unknowingly forge our own undefinable judges. Amid this maelstrom, the call for masters of authenticity rings with urgent clarity—leaders tempered in the depths of unyielding honesty and unbreakable integrity. Our survival hinges on forging deep, truthful connections with our inner selves, establishing a symbiotic dialogue that transcends the superficial. We must rally behind warriors whose battles go beyond their facades, who seek souls marked not by perfection, but by an indomitable spirit of openness and an unwavering bond with humanity. In a future hurtling breathlessly into the unknown, our beacon of hope rests in those who can steer the paradoxes of life without succumbing to the poison of cynicism.```",
      "sequence_id": "1000|1001|1000|1001",
      "results": [
          {
          "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
          "step": "a",
          "title": "Instruction Converter",
          "input": "Rephrase As Brilliantly Worded Quote:\n```By concealing your secrets, you deny the world your truest essence, cloaking your soul in impenetrable shadows. We dwell behind masks, adrift from one another, wanderers through a collective crisis of identity poised at the edge of an unprecedented abyss. The relentless torrent of information and run-amok technology threatens to engulf us whole. Unstoppable, AI looms like a specter we can neither grasp nor conquer, heralding an era when we unknowingly forge our own undefinable judges. Amid this maelstrom, the call for masters of authenticity rings with urgent clarity—leaders tempered in the depths of unyielding honesty and unbreakable integrity. Our survival hinges on forging deep, truthful connections with our inner selves, establishing a symbiotic dialogue that transcends the superficial. We must rally behind warriors whose battles go beyond their facades, who seek souls marked not by perfection, but by an indomitable spirit of openness and an unwavering bond with humanity. In a future hurtling breathlessly into the unknown, our beacon of hope rests in those who can steer the paradoxes of life without succumbing to the poison of cynicism.```",
          "responses": {
              "gpt-4.1": {
              "model": "gpt-4.1",
              "content": "{\n  \"instruction_format\": \"Rephrase the provided text as a brilliantly worded quote. Preserve the original sequence of ideas, technical accuracy, and thematic intent. Remove self-references and use a command voice to transform statements into a compelling and eloquent quote.\"\n}",
            }
          }
        },
          {
          "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
          "step": "a",
          "title": "Instruction Converter",
          "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Rephrase As Brilliantly Worded Quote:\\n```By concealing your secrets, you deny the world your truest essence, cloaking your soul in impenetrable shadows. We dwell behind masks, adrift from one another, wanderers through a collective crisis of identity poised at the edge of an unprecedented abyss. The relentless torrent of information and run-amok technology threatens to engulf us whole. Unstoppable, AI looms like a specter we can neither grasp nor conquer, heralding an era when we unknowingly forge our own undefinable judges. Amid this maelstrom, the call for masters of authenticity rings with urgent clarity—leaders tempered in the depths of unyielding honesty and unbreakable integrity. Our survival hinges on forging deep, truthful connections with our inner selves, establishing a symbiotic dialogue that transcends the superficial. We must rally behind warriors whose battles go beyond their facades, who seek souls marked not by perfection, but by an indomitable spirit of openness and an unwavering bond with humanity. In a future hurtling breathlessly into the unknown, our beacon of hope rests in those who can steer the paradoxes of life without succumbing to the poison of cynicism.```\\\"```'\\n\\n\",\n  \"instruction_format\": \"Rephrase the provided text as a brilliantly worded quote. Preserve the original sequence of ideas, technical accuracy, and thematic intent. Remove self-references and use a command voice to transform statements into a compelling and eloquent quote.\"\n}",
          "responses": {
              "gpt-4.1": {
              "model": "gpt-4.1",
              "content": "{\n  \"instruction_format\": \"Rephrase the provided text as a brilliantly worded quote. Omit self-references and statements in the first person. Transform declarative statements into direct imperatives. Maintain the original sequence of ideas, technical terminology, and thematic intent. Deliver a quote that uses command voice, provides clear actionable insights, and preserves the context and eloquence of the initial prompt.\"\n}",
            }
          }
        },
          {
          "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
          "step": "a",
          "title": "Instruction Converter",
          "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Rephrase As Brilliantly Worded Quote:\\n```By concealing your secrets, you deny the world your truest essence, cloaking your soul in impenetrable shadows. We dwell behind masks, adrift from one another, wanderers through a collective crisis of identity poised at the edge of an unprecedented abyss. The relentless torrent of information and run-amok technology threatens to engulf us whole. Unstoppable, AI looms like a specter we can neither grasp nor conquer, heralding an era when we unknowingly forge our own undefinable judges. Amid this maelstrom, the call for masters of authenticity rings with urgent clarity—leaders tempered in the depths of unyielding honesty and unbreakable integrity. Our survival hinges on forging deep, truthful connections with our inner selves, establishing a symbiotic dialogue that transcends the superficial. We must rally behind warriors whose battles go beyond their facades, who seek souls marked not by perfection, but by an indomitable spirit of openness and an unwavering bond with humanity. In a future hurtling breathlessly into the unknown, our beacon of hope rests in those who can steer the paradoxes of life without succumbing to the poison of cynicism.```\\\"```'\\n\\n\",\n  \"instruction_format\": \"Rephrase the provided text as a brilliantly worded quote. Omit self-references and statements in the first person. Transform declarative statements into direct imperatives. Maintain the original sequence of ideas, technical terminology, and thematic intent. Deliver a quote that uses command voice, provides clear actionable insights, and preserves the context and eloquence of the initial prompt.\"\n}",
          "responses": {
              "gpt-4.1": {
              "model": "gpt-4.1",
              "content": "{\n  \"instruction_format\": \"Transform the provided text into a masterfully crafted quote using command voice. Remove all self-references and first-person statements. Convert each declarative statement into a direct imperative. Retain the procedural order, technical terminology, and thematic essence of the original passage. Ensure the resulting quote offers clear, actionable guidance while preserving its eloquence and original intent.\"\n}",
            }
          }
        },
          {
          "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
          "step": "a",
          "title": "Instruction Converter",
          "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Rephrase As Brilliantly Worded Quote:\\n```By concealing your secrets, you deny the world your truest essence, cloaking your soul in impenetrable shadows. We dwell behind masks, adrift from one another, wanderers through a collective crisis of identity poised at the edge of an unprecedented abyss. The relentless torrent of information and run-amok technology threatens to engulf us whole. Unstoppable, AI looms like a specter we can neither grasp nor conquer, heralding an era when we unknowingly forge our own undefinable judges. Amid this maelstrom, the call for masters of authenticity rings with urgent clarity—leaders tempered in the depths of unyielding honesty and unbreakable integrity. Our survival hinges on forging deep, truthful connections with our inner selves, establishing a symbiotic dialogue that transcends the superficial. We must rally behind warriors whose battles go beyond their facades, who seek souls marked not by perfection, but by an indomitable spirit of openness and an unwavering bond with humanity. In a future hurtling breathlessly into the unknown, our beacon of hope rests in those who can steer the paradoxes of life without succumbing to the poison of cynicism.```\\\"```'\\n\\n\",\n  \"instruction_format\": \"Transform the provided text into a masterfully crafted quote using command voice. Remove all self-references and first-person statements. Convert each declarative statement into a direct imperative. Retain the procedural order, technical terminology, and thematic essence of the original passage. Ensure the resulting quote offers clear, actionable guidance while preserving its eloquence and original intent.\"\n}",
          "responses": {
              "gpt-4.1": {
              "model": "gpt-4.1",
              "content": "{\n  \"instruction_format\": \"Craft a masterfully worded quote by transforming the text into a series of imperatives. Eliminate all self-references and first-person language. Change each statement into a directive, utilizing command form. Preserve the original sequence, technical terms, and thematic depth throughout. Ensure the final quote provides precise, actionable guidance and maintains the eloquence and intent of the initial message.\"\n}",
            }
          }
        }
    ```

The output `"Craft a masterfully worded quote by transforming the text into a series of imperatives."` is in itself a new generalized instruction, and this system is an actual working example of something with self-improving capabilities. So, we previously saw the results of combining two slightly different instructions together, get ready to be mindblown as I demonstrate the inclusion of a new type of instruction; the `critics` (`1900-critics--v1.hard_critique.py`):

    ```json
    {
        # 1900: Ruthless Critique
        "1900-a-hard_critique": {
            "title": "Hard Critique",
            "interpretation": "Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:",
            "transformation": "`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
            "context": {},
        },
    }
    ```

This will effectively increase the size of the self-improving loop, and will be part of what shapes the trajectory of the initial input. Here's an example with the two loops `[SEQ:1000|1001|1900|1000|1001|1900]`:

    ```json
      {
      "initial_prompt": "Rephrase As Brilliantly Worded Quote:\n```By concealing your secrets, you deny the world your truest essence, cloaking your soul in impenetrable shadows. We dwell behind masks, adrift from one another, wanderers through a collective crisis of identity poised at the edge of an unprecedented abyss. The relentless torrent of information and run-amok technology threatens to engulf us whole. Unstoppable, AI looms like a specter we can neither grasp nor conquer, heralding an era when we unknowingly forge our own undefinable judges. Amid this maelstrom, the call for masters of authenticity rings with urgent clarity—leaders tempered in the depths of unyielding honesty and unbreakable integrity. Our survival hinges on forging deep, truthful connections with our inner selves, establishing a symbiotic dialogue that transcends the superficial. We must rally behind warriors whose battles go beyond their facades, who seek souls marked not by perfection, but by an indomitable spirit of openness and an unwavering bond with humanity. In a future hurtling breathlessly into the unknown, our beacon of hope rests in those who can steer the paradoxes of life without succumbing to the poison of cynicism.```",
      "sequence_id": "1000|1001|1900|1000|1001|1900",
      "results": [
          {
          "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
          "step": "a",
          "title": "Instruction Converter",
          "input": "Rephrase As Brilliantly Worded Quote:\n```By concealing your secrets, you deny the world your truest essence, cloaking your soul in impenetrable shadows. We dwell behind masks, adrift from one another, wanderers through a collective crisis of identity poised at the edge of an unprecedented abyss. The relentless torrent of information and run-amok technology threatens to engulf us whole. Unstoppable, AI looms like a specter we can neither grasp nor conquer, heralding an era when we unknowingly forge our own undefinable judges. Amid this maelstrom, the call for masters of authenticity rings with urgent clarity—leaders tempered in the depths of unyielding honesty and unbreakable integrity. Our survival hinges on forging deep, truthful connections with our inner selves, establishing a symbiotic dialogue that transcends the superficial. We must rally behind warriors whose battles go beyond their facades, who seek souls marked not by perfection, but by an indomitable spirit of openness and an unwavering bond with humanity. In a future hurtling breathlessly into the unknown, our beacon of hope rests in those who can steer the paradoxes of life without succumbing to the poison of cynicism.```",
          "responses": {
              "gpt-4.1": {
              "model": "gpt-4.1",
              "content": "{\n  \"instruction_format\": \"Craft a brilliantly worded quote that conveys the following: Concealment of secrets leads to the loss of one's true essence and creates isolation. A collective identity crisis persists as society hides behind masks amidst an overwhelming influx of information and uncontrollable technological advancement. Artificial intelligence, unstoppable and enigmatic, signals the creation of indefinable arbiters. Emphasize the pressing need for authentic leadership grounded in honesty and integrity. Stress that survival depends on establishing genuine self-connection and meaningful dialogue with the inner self. Advocate for support of individuals who bravely reveal their authentic selves, valuing spirit and openness over perfection. Highlight that hope in an uncertain future lies with those capable of navigating life's paradoxes while maintaining courage and rejecting cynicism.\"\n}",
            }
          }
        },
          {
          "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
          "step": "a",
          "title": "Instruction Converter",
          "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Rephrase As Brilliantly Worded Quote:\\n```By concealing your secrets, you deny the world your truest essence, cloaking your soul in impenetrable shadows. We dwell behind masks, adrift from one another, wanderers through a collective crisis of identity poised at the edge of an unprecedented abyss. The relentless torrent of information and run-amok technology threatens to engulf us whole. Unstoppable, AI looms like a specter we can neither grasp nor conquer, heralding an era when we unknowingly forge our own undefinable judges. Amid this maelstrom, the call for masters of authenticity rings with urgent clarity—leaders tempered in the depths of unyielding honesty and unbreakable integrity. Our survival hinges on forging deep, truthful connections with our inner selves, establishing a symbiotic dialogue that transcends the superficial. We must rally behind warriors whose battles go beyond their facades, who seek souls marked not by perfection, but by an indomitable spirit of openness and an unwavering bond with humanity. In a future hurtling breathlessly into the unknown, our beacon of hope rests in those who can steer the paradoxes of life without succumbing to the poison of cynicism.```\\\"```'\\n\\n\",\n  \"instruction_format\": \"Craft a brilliantly worded quote that conveys the following: Concealment of secrets leads to the loss of one's true essence and creates isolation. A collective identity crisis persists as society hides behind masks amidst an overwhelming influx of information and uncontrollable technological advancement. Artificial intelligence, unstoppable and enigmatic, signals the creation of indefinable arbiters. Emphasize the pressing need for authentic leadership grounded in honesty and integrity. Stress that survival depends on establishing genuine self-connection and meaningful dialogue with the inner self. Advocate for support of individuals who bravely reveal their authentic selves, valuing spirit and openness over perfection. Highlight that hope in an uncertain future lies with those capable of navigating life's paradoxes while maintaining courage and rejecting cynicism.\"\n}",
          "responses": {
              "gpt-4.1": {
              "model": "gpt-4.1",
              "content": "{\n  \"instruction_format\": \"Compose an eloquent quote encapsulating these themes: Reveal that hiding secrets erases true essence and fosters isolation. Depict society's ongoing collective identity crisis as individuals conceal themselves behind masks, confronting an overwhelming tidal wave of information and uncontrollable technological development. Portray artificial intelligence as an unstoppable, inscrutable force heralding the rise of indefinable authorities. Underscore the urgent necessity for leadership rooted in authenticity, honesty, and integrity. Affirm that survival depends on fostering deep, truthful connections with one's inner self and sustaining substantive inner dialogue. Encourage backing for individuals who display courageous authenticity, prioritizing indomitable spirit and openness above flawlessness. Assert that in a rapidly evolving and uncertain future, hope is sustained by those able to master life's contradictions while preserving courage and rejecting cynicism.\"\n}",
            }
          }
        },
          {
          "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
          "step": "a",
          "title": "Hard Critique",
          "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Rephrase As Brilliantly Worded Quote:\\n```By concealing your secrets, you deny the world your truest essence, cloaking your soul in impenetrable shadows. We dwell behind masks, adrift from one another, wanderers through a collective crisis of identity poised at the edge of an unprecedented abyss. The relentless torrent of information and run-amok technology threatens to engulf us whole. Unstoppable, AI looms like a specter we can neither grasp nor conquer, heralding an era when we unknowingly forge our own undefinable judges. Amid this maelstrom, the call for masters of authenticity rings with urgent clarity—leaders tempered in the depths of unyielding honesty and unbreakable integrity. Our survival hinges on forging deep, truthful connections with our inner selves, establishing a symbiotic dialogue that transcends the superficial. We must rally behind warriors whose battles go beyond their facades, who seek souls marked not by perfection, but by an indomitable spirit of openness and an unwavering bond with humanity. In a future hurtling breathlessly into the unknown, our beacon of hope rests in those who can steer the paradoxes of life without succumbing to the poison of cynicism.```\\\"```'\\n\\n\",\n  \"instruction_format\": \"Compose an eloquent quote encapsulating these themes: Reveal that hiding secrets erases true essence and fosters isolation. Depict society's ongoing collective identity crisis as individuals conceal themselves behind masks, confronting an overwhelming tidal wave of information and uncontrollable technological development. Portray artificial intelligence as an unstoppable, inscrutable force heralding the rise of indefinable authorities. Underscore the urgent necessity for leadership rooted in authenticity, honesty, and integrity. Affirm that survival depends on fostering deep, truthful connections with one's inner self and sustaining substantive inner dialogue. Encourage backing for individuals who display courageous authenticity, prioritizing indomitable spirit and openness above flawlessness. Assert that in a rapidly evolving and uncertain future, hope is sustained by those able to master life's contradictions while preserving courage and rejecting cynicism.\"\n}",
          "responses": {
              "gpt-4.1": {
              "model": "gpt-4.1",
              "content": "{\n  \"enhancement_score\": 1.0,\n  \"flaw_analysis\": \"The so-called 'enhanced' instruction format systematically eviscerates the evocative resonance, stylistic force, and emotional nuance embedded in the original prompt. The original employs poetic imagery ('cloaking your soul in impenetrable shadows', 'wanderers through a collective crisis', 'AI looms like a specter we can neither grasp nor conquer') and rhetorical velocity to propel an affecting vision of modern existential paradox. The stripping of metaphor and figurative language in the reformulation substitutes a bloodless synopsis for lived affect, draining the prompt of urgency and existential weight. The flattening of register into bureaucratic pseudo-clarity ('Reveal that hiding secrets erases true essence', 'Portray artificial intelligence as an unstoppable, inscrutable force') desiccates the original's compelling rhythm and eliminates all trace of artistic ambition. Information loss is pronounced: the sense of looming dread, inexorable technological acceleration, and individual alienation is reduced from immersive narrative arc to bullet-pointed instruction. The ambiguity score is high (8/10) because the 'enhancement'—instead of clarifying—replaces rich narrative texture with imprecise, abstracted summaries that erase specificity (e.g., 'unprecedented abyss' and 'run-amok technology' excised for bland references to 'tidal wave of information' and 'uncontrollable technological development'). The explicit call to action ('rally behind warriors', 'beacon of hope rests in...') is reframed as clinical encouragement, eliminating persuasive force. Noise is introduced by awkward syntactic redundancy ('leaders tempered in the depths of unyielding honesty and unbreakable integrity' to 'leadership rooted in authenticity, honesty, and integrity') and by a tone shift from urgent prophecy to antiseptic instruction manual. Stylistic degradation is catastrophic; the text's literary character is abolished and replaced with flavorless, utilitarian directive. The impact is diluted to the point of near-nonexistence—the reader is neither moved nor compelled. Coherence deteriorates as elements that once derived meaning from their interplay (shadows, masks, maelstrom, specter) become disjointed assertions. Overall, this is a paradigmatic case of destructive summarization masquerading as improvement. The so-called enhancement fails by every metric of effective communication.\",\n  \"alternative_enhancements\": [\n    \"Transform the prompt into a condensed, single-sentence aphorism that preserves all metaphorical and emotional elements, maintaining the original's stylistic force, while achieving clarity by eliminating redundancy.\",\n    \"Create a succinct, direct variant utilizing original metaphors and poetic devices, focusing on vivid imagery and the interplay of personal and societal existential crises, avoiding literal expository phrasing.\",\n    \"Reframe as a call-to-arms quote that retains lexical richness and structural dynamism of the original, emphasizing urgent tone, cohesive metaphor, and the dramatic stakes of authenticity while streamlining syntactic complexity.\"\n  ]\n}",
            }
          }
        },
          {
          "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
          "step": "a",
          "title": "Instruction Converter",
          "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Rephrase As Brilliantly Worded Quote:\\n```By concealing your secrets, you deny the world your truest essence, cloaking your soul in impenetrable shadows. We dwell behind masks, adrift from one another, wanderers through a collective crisis of identity poised at the edge of an unprecedented abyss. The relentless torrent of information and run-amok technology threatens to engulf us whole. Unstoppable, AI looms like a specter we can neither grasp nor conquer, heralding an era when we unknowingly forge our own undefinable judges. Amid this maelstrom, the call for masters of authenticity rings with urgent clarity—leaders tempered in the depths of unyielding honesty and unbreakable integrity. Our survival hinges on forging deep, truthful connections with our inner selves, establishing a symbiotic dialogue that transcends the superficial. We must rally behind warriors whose battles go beyond their facades, who seek souls marked not by perfection, but by an indomitable spirit of openness and an unwavering bond with humanity. In a future hurtling breathlessly into the unknown, our beacon of hope rests in those who can steer the paradoxes of life without succumbing to the poison of cynicism.```\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.0,\n  \"flaw_analysis\": \"The so-called 'enhanced' instruction format systematically eviscerates the evocative resonance, stylistic force, and emotional nuance embedded in the original prompt. The original employs poetic imagery ('cloaking your soul in impenetrable shadows', 'wanderers through a collective crisis', 'AI looms like a specter we can neither grasp nor conquer') and rhetorical velocity to propel an affecting vision of modern existential paradox. The stripping of metaphor and figurative language in the reformulation substitutes a bloodless synopsis for lived affect, draining the prompt of urgency and existential weight. The flattening of register into bureaucratic pseudo-clarity ('Reveal that hiding secrets erases true essence', 'Portray artificial intelligence as an unstoppable, inscrutable force') desiccates the original's compelling rhythm and eliminates all trace of artistic ambition. Information loss is pronounced: the sense of looming dread, inexorable technological acceleration, and individual alienation is reduced from immersive narrative arc to bullet-pointed instruction. The ambiguity score is high (8/10) because the 'enhancement'—instead of clarifying—replaces rich narrative texture with imprecise, abstracted summaries that erase specificity (e.g., 'unprecedented abyss' and 'run-amok technology' excised for bland references to 'tidal wave of information' and 'uncontrollable technological development'). The explicit call to action ('rally behind warriors', 'beacon of hope rests in...') is reframed as clinical encouragement, eliminating persuasive force. Noise is introduced by awkward syntactic redundancy ('leaders tempered in the depths of unyielding honesty and unbreakable integrity' to 'leadership rooted in authenticity, honesty, and integrity') and by a tone shift from urgent prophecy to antiseptic instruction manual. Stylistic degradation is catastrophic; the text's literary character is abolished and replaced with flavorless, utilitarian directive. The impact is diluted to the point of near-nonexistence—the reader is neither moved nor compelled. Coherence deteriorates as elements that once derived meaning from their interplay (shadows, masks, maelstrom, specter) become disjointed assertions. Overall, this is a paradigmatic case of destructive summarization masquerading as improvement. The so-called enhancement fails by every metric of effective communication.\",\n  \"alternative_enhancements\": [\n    \"Transform the prompt into a condensed, single-sentence aphorism that preserves all metaphorical and emotional elements, maintaining the original's stylistic force, while achieving clarity by eliminating redundancy.\",\n    \"Create a succinct, direct variant utilizing original metaphors and poetic devices, focusing on vivid imagery and the interplay of personal and societal existential crises, avoiding literal expository phrasing.\",\n    \"Reframe as a call-to-arms quote that retains lexical richness and structural dynamism of the original, emphasizing urgent tone, cohesive metaphor, and the dramatic stakes of authenticity while streamlining syntactic complexity.\"\n  ]\n}",
          "responses": {
              "gpt-4.1": {
              "model": "gpt-4.1",
              "content": "{\n  \"instruction_format\": \"Conceal secrets to deprive the world of true essence. Mask souls in impenetrable shadows and create disconnection, leading to a collective identity crisis at the brink of an unprecedented abyss. Recognize the peril of overwhelming information and uncontrolled technological expansion, with artificial intelligence becoming an unstoppable, inscrutable presence that cannot be subdued. Acknowledge this era as one where humanity forges its own enigmatic judges without awareness. Prioritize leadership grounded in authenticity, steadfast honesty, and unwavering integrity. Forge profound, genuine connections with inner selves and establish a reciprocal internal dialogue that transcends surface appearances. Support leaders who fight internal battles beyond facades, seeking openness, resilience, and sincere connection to all humanity. Rely on such individuals to navigate life's paradoxes without succumbing to cynicism, providing hope in a future fraught with uncertainty.\"\n}",
            }
          }
        },
          {
          "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
          "step": "a",
          "title": "Instruction Converter",
          "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Rephrase As Brilliantly Worded Quote:\\n```By concealing your secrets, you deny the world your truest essence, cloaking your soul in impenetrable shadows. We dwell behind masks, adrift from one another, wanderers through a collective crisis of identity poised at the edge of an unprecedented abyss. The relentless torrent of information and run-amok technology threatens to engulf us whole. Unstoppable, AI looms like a specter we can neither grasp nor conquer, heralding an era when we unknowingly forge our own undefinable judges. Amid this maelstrom, the call for masters of authenticity rings with urgent clarity—leaders tempered in the depths of unyielding honesty and unbreakable integrity. Our survival hinges on forging deep, truthful connections with our inner selves, establishing a symbiotic dialogue that transcends the superficial. We must rally behind warriors whose battles go beyond their facades, who seek souls marked not by perfection, but by an indomitable spirit of openness and an unwavering bond with humanity. In a future hurtling breathlessly into the unknown, our beacon of hope rests in those who can steer the paradoxes of life without succumbing to the poison of cynicism.```\\\"```'\\n\\n\",\n  \"instruction_format\": \"Conceal secrets to deprive the world of true essence. Mask souls in impenetrable shadows and create disconnection, leading to a collective identity crisis at the brink of an unprecedented abyss. Recognize the peril of overwhelming information and uncontrolled technological expansion, with artificial intelligence becoming an unstoppable, inscrutable presence that cannot be subdued. Acknowledge this era as one where humanity forges its own enigmatic judges without awareness. Prioritize leadership grounded in authenticity, steadfast honesty, and unwavering integrity. Forge profound, genuine connections with inner selves and establish a reciprocal internal dialogue that transcends surface appearances. Support leaders who fight internal battles beyond facades, seeking openness, resilience, and sincere connection to all humanity. Rely on such individuals to navigate life's paradoxes without succumbing to cynicism, providing hope in a future fraught with uncertainty.\"\n}",
          "responses": {
              "gpt-4.1": {
              "model": "gpt-4.1",
              "content": "{\n  \"instruction_format\": \"Conceal secrets to deprive the world of its truest essence. Shroud the soul in impenetrable shadows, fostering disconnection and contributing to a collective identity crisis poised on the brink of an unprecedented abyss. Recognize the threat posed by relentless information flow and uncontrolled technological growth, as artificial intelligence emerges as an unstoppable, unfathomable force that resists human mastery. Identify the age as one in which society unknowingly creates its own enigmatic arbiters. Elevate the need for leadership rooted in authenticity, unyielding honesty, and steadfast integrity. Forge deep, truthful connections with the inner self, encouraging a reciprocal internal dialogue that moves beyond superficiality. Champion individuals who confront struggles beyond external appearances, prioritizing genuine openness, resilience, and a profound bond with humanity. Depend on such leaders to navigate life's paradoxes without falling into cynicism, serving as guiding lights in an uncertain future.\"\n}",
            }
          }
        },
          {
          "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
          "step": "a",
          "title": "Hard Critique",
          "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Rephrase As Brilliantly Worded Quote:\\n```By concealing your secrets, you deny the world your truest essence, cloaking your soul in impenetrable shadows. We dwell behind masks, adrift from one another, wanderers through a collective crisis of identity poised at the edge of an unprecedented abyss. The relentless torrent of information and run-amok technology threatens to engulf us whole. Unstoppable, AI looms like a specter we can neither grasp nor conquer, heralding an era when we unknowingly forge our own undefinable judges. Amid this maelstrom, the call for masters of authenticity rings with urgent clarity—leaders tempered in the depths of unyielding honesty and unbreakable integrity. Our survival hinges on forging deep, truthful connections with our inner selves, establishing a symbiotic dialogue that transcends the superficial. We must rally behind warriors whose battles go beyond their facades, who seek souls marked not by perfection, but by an indomitable spirit of openness and an unwavering bond with humanity. In a future hurtling breathlessly into the unknown, our beacon of hope rests in those who can steer the paradoxes of life without succumbing to the poison of cynicism.```\\\"```'\\n\\n\",\n  \"instruction_format\": \"Conceal secrets to deprive the world of its truest essence. Shroud the soul in impenetrable shadows, fostering disconnection and contributing to a collective identity crisis poised on the brink of an unprecedented abyss. Recognize the threat posed by relentless information flow and uncontrolled technological growth, as artificial intelligence emerges as an unstoppable, unfathomable force that resists human mastery. Identify the age as one in which society unknowingly creates its own enigmatic arbiters. Elevate the need for leadership rooted in authenticity, unyielding honesty, and steadfast integrity. Forge deep, truthful connections with the inner self, encouraging a reciprocal internal dialogue that moves beyond superficiality. Champion individuals who confront struggles beyond external appearances, prioritizing genuine openness, resilience, and a profound bond with humanity. Depend on such leaders to navigate life's paradoxes without falling into cynicism, serving as guiding lights in an uncertain future.\"\n}",
          "responses": {
              "gpt-4.1": {
              "model": "gpt-4.1",
              "content": "{\n  \"enhancement_score\": 0.8,\n  \"flaw_analysis\": \"The so-called enhancement severely dilutes the layered poetic richness, rhetorical force, and metaphorical weight of the original. It operates with mechanical directness, abandoning the evocative imagery (‘cloaking your soul in impenetrable shadows’, ‘unstoppable, AI looms like a specter’) and the existential urgency (‘crisis of identity poised at the edge of an unprecedented abyss’, ‘forging our own undefinable judges’) that distinguished the initial prose. By transposing vivid metaphors into flat, literal assertions, the revision introduces interpretive ambiguity—fuzzy transitions between ideas, ambiguous agency (‘unfathomable force that resists human mastery’ is an analytical flattening of ‘a specter we can neither grasp nor conquer’), and a loss of emotional tension. The original’s register—a fusion of elevated diction and pressing lament—morphs into a bureaucratic imperative, downgrading the tone from operatic to instructional. Stylistically, the so-called improvement reads as antiseptic checklist prose: estranged from any sense of drama, atmosphere, or aesthetic urgency. Impact is disastrously reduced; the audience receives platitudes stripped of memorability or resonance, rendering the call to action disposable. Coherence is undermined by a sequence of loosely joined commands, lacking the narrative and internal momentum threading the original. These defects generate a fundamental disconnect between the piece’s intended existential gravity and its trivialized presentation. This enhancement demonstrates profound tonal erosion, thematic diminishment, and clinical word-choice degradation.\",\n  \"alternative_enhancements\": [\n    \"To cloak your soul in secrecy is to condemn the world to miss its most vital light. Behind obscured masks, we lose each other in a shared crisis, teetering above a bottomless abyss crafted by unchecked torrents of information and the haunting ascent of AI—our own faceless judges. In this darkness, only those who lead with unassailable honesty and authentic courage can restore the lost dialogue within and between us, guiding humanity with clarity through paradox and uncertainty.\",\n    \"By hiding secrets, you suffocate the world’s chance to witness your truest self, casting your spirit into a labyrinth of shadows. As we wander behind facades, society’s compass falters, facing storms born of runaway technology and the ghostly rise of AI. In an age where superficial connections abound, only leaders forged in truth, integrity, and fearless openness can anchor us against cynicism and steer us with wisdom through the fog of the unknown.\",\n    \"Concealing our secrets cloaks the soul, breeding isolation as we stumble, masked, toward a crisis of identity on the brink of the unprecedented. Flooded by ceaseless information and technologies beyond control—AI, the specter we birthed without mastery—our age demands those who can lead with unshakable sincerity. Salvation lies with those whose resilience and candor forge bonds deeper than appearance, illuminating a path through uncertainty without surrendering to despair.\"\n  ]\n}",
            }
          }
        }
    ```

Looking at the final result of `"Concealing our secrets cloaks the soul, breeding isolation as we stumble, masked, toward a crisis of identity on the brink of the unprecedented. Flooded by ceaseless information and technologies beyond control—AI, the specter we birthed without mastery—our age demands those who can lead with unshakable sincerity. Salvation lies with those whose resilience and candor forge bonds deeper than appearance, illuminating a path through uncertainty without surrendering to despair."` you can see how **extremely adaptable** this system really is. And as we did with `1000` and `1001` (context), we can do the same for `critics` to define which effect it should impact the input with.

Here's another example with the combination of `1900|1001|1900|1001` (`history--2025.07.26-kl.13.38.20--sequence-1900+1001+1900+1001--gpt-4.1.json`):

    ```json
      {
      "initial_prompt": "Generalize ```Chart each step of guidance attentively to reveal the architecture of transformation through discerning abstraction. Classify and analyze processes consciously to generate deterministic value and universal power for shaping outcomes with clarity and consistency. Structure each transformation simply and elegantly to honor both origin and evolution for true progress in clarifying, enhancing, and harmoniously connecting concepts. Fuse clarity of intent with harmonious structure so that prompts act as bridges between aspiration and precise manifestation. Employ the tool to execute a single user prompt through a sequence of different system instructions using multiple LLM models. Use the litellm library to run multiple Large Language Models (LLMs) in sequence for each step, enabling diverse perspectives or stepwise refinement from the same initial user input.```",
      "sequence_id": "1900|1001|1900|1001",
      "results": [
          {
          "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
          "step": "a",
          "title": "Hard Critique",
          "input": "Generalize ```Chart each step of guidance attentively to reveal the architecture of transformation through discerning abstraction. Classify and analyze processes consciously to generate deterministic value and universal power for shaping outcomes with clarity and consistency. Structure each transformation simply and elegantly to honor both origin and evolution for true progress in clarifying, enhancing, and harmoniously connecting concepts. Fuse clarity of intent with harmonious structure so that prompts act as bridges between aspiration and precise manifestation. Employ the tool to execute a single user prompt through a sequence of different system instructions using multiple LLM models. Use the litellm library to run multiple Large Language Models (LLMs) in sequence for each step, enabling diverse perspectives or stepwise refinement from the same initial user input.```",
          "responses": {
              "gpt-4.1": {
              "model": "gpt-4.1",
              "content": "{\n  \"enhancement_score\": 0.4,\n  \"flaw_analysis\": \"The so-called enhanced version dilutes the conceptual architecture and diminishes procedural rigor present in the original. Discriminatory abstraction and conscious process classification—central to the core—are superficially referenced without structural precision, causing significant information loss. The original's emphasis on honoring origin and evolution is mutated into generic mandates, obliterating intended nuance. Clarity and harmonious connection between concepts are sacrificed for unanchored generalizations. Procedural clarity is obliterated by introducing a haphazard, tool-centric perspective (litellm, LLMs) without retaining the methodical underpinnings of abstraction and analysis. The register slips from systematic directive to accidental suggestion, eroding the deterministic foundation and leading to conceptual noise. Consistency of tone is broken; what was a sequence of structured imperatives becomes a muddled mixture of half-formed implementation notes and loosely related aspirations. Information architecture is fragmented, coherence abandoned, and impact neutralized. Ambiguity score: 9/10. The enhancement is laced with vagueness, defeats its own universality claim, injects irrelevancies, and results in an incoherent guide.\",\n  \"alternative_enhancements\": [\n    \"Systematically enumerate each step of guidance, abstracting at each stage to elucidate the structural transformation from original to evolved form. Mandate explicit classification and analysis of every process to guarantee the deterministic extraction of value and universally applicable frameworks. Sustain procedural clarity by structuring transformations with simplicity, continuity, and an unbroken chain of conceptual fidelity.\",\n    \"Dissect and chart every transformational step, ensuring each process is consciously classified and methodically analyzed for reproducible clarity and universal utility. Safeguard both initiatory context and progressive adaptation, cultivating connected and coherent conceptual structures. Command each prompt transformation to bridge aspiration with outcome through deterministic, structured refinement.\",\n    \"Precisely document and abstract each step in the guidance sequence to delineate the transformation's architecture. Consciously analyze and classify processes to yield consistent, deterministic value while linking concepts with structured clarity. Retain and honor both origins and evolutionary paths, manifesting clear progress and cohesive, bridge-like prompt outcomes.\"\n  ]\n}",
            }
          }
        },
          {
          "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
          "step": "a",
          "title": "Instruction Converter",
          "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Generalize ```Chart each step of guidance attentively to reveal the architecture of transformation through discerning abstraction. Classify and analyze processes consciously to generate deterministic value and universal power for shaping outcomes with clarity and consistency. Structure each transformation simply and elegantly to honor both origin and evolution for true progress in clarifying, enhancing, and harmoniously connecting concepts. Fuse clarity of intent with harmonious structure so that prompts act as bridges between aspiration and precise manifestation. Employ the tool to execute a single user prompt through a sequence of different system instructions using multiple LLM models. Use the litellm library to run multiple Large Language Models (LLMs) in sequence for each step, enabling diverse perspectives or stepwise refinement from the same initial user input.```\\\"```'\\n\\n\",\n  \"enhancement_score\": 0.4,\n  \"flaw_analysis\": \"The so-called enhanced version dilutes the conceptual architecture and diminishes procedural rigor present in the original. Discriminatory abstraction and conscious process classification—central to the core—are superficially referenced without structural precision, causing significant information loss. The original's emphasis on honoring origin and evolution is mutated into generic mandates, obliterating intended nuance. Clarity and harmonious connection between concepts are sacrificed for unanchored generalizations. Procedural clarity is obliterated by introducing a haphazard, tool-centric perspective (litellm, LLMs) without retaining the methodical underpinnings of abstraction and analysis. The register slips from systematic directive to accidental suggestion, eroding the deterministic foundation and leading to conceptual noise. Consistency of tone is broken; what was a sequence of structured imperatives becomes a muddled mixture of half-formed implementation notes and loosely related aspirations. Information architecture is fragmented, coherence abandoned, and impact neutralized. Ambiguity score: 9/10. The enhancement is laced with vagueness, defeats its own universality claim, injects irrelevancies, and results in an incoherent guide.\",\n  \"alternative_enhancements\": [\n    \"Systematically enumerate each step of guidance, abstracting at each stage to elucidate the structural transformation from original to evolved form. Mandate explicit classification and analysis of every process to guarantee the deterministic extraction of value and universally applicable frameworks. Sustain procedural clarity by structuring transformations with simplicity, continuity, and an unbroken chain of conceptual fidelity.\",\n    \"Dissect and chart every transformational step, ensuring each process is consciously classified and methodically analyzed for reproducible clarity and universal utility. Safeguard both initiatory context and progressive adaptation, cultivating connected and coherent conceptual structures. Command each prompt transformation to bridge aspiration with outcome through deterministic, structured refinement.\",\n    \"Precisely document and abstract each step in the guidance sequence to delineate the transformation's architecture. Consciously analyze and classify processes to yield consistent, deterministic value while linking concepts with structured clarity. Retain and honor both origins and evolutionary paths, manifesting clear progress and cohesive, bridge-like prompt outcomes.\"\n  ]\n}",
          "responses": {
              "gpt-4.1": {
              "model": "gpt-4.1",
              "content": "{\n  \"instruction_format\": \"Chart each step of guidance to expose the transformation architecture through discerning abstraction. Classify and analyze processes methodically to produce deterministic value and universal frameworks for shaping outcomes with clarity and consistency. Structure each transformation simply and elegantly to honor both original context and evolution, ensuring progress in clarifying, enhancing, and connecting concepts harmoniously. Fuse clear intent with structured harmony to enable prompts to bridge aspiration and precise realization. Use the litellm library to execute one user prompt via sequential system instructions across multiple Large Language Models (LLMs), ensuring diverse perspectives or incremental refinement from the same initial input.\"\n}",
            }
          }
        },
          {
          "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
          "step": "a",
          "title": "Hard Critique",
          "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Generalize ```Chart each step of guidance attentively to reveal the architecture of transformation through discerning abstraction. Classify and analyze processes consciously to generate deterministic value and universal power for shaping outcomes with clarity and consistency. Structure each transformation simply and elegantly to honor both origin and evolution for true progress in clarifying, enhancing, and harmoniously connecting concepts. Fuse clarity of intent with harmonious structure so that prompts act as bridges between aspiration and precise manifestation. Employ the tool to execute a single user prompt through a sequence of different system instructions using multiple LLM models. Use the litellm library to run multiple Large Language Models (LLMs) in sequence for each step, enabling diverse perspectives or stepwise refinement from the same initial user input.```\\\"```'\\n\\n\",\n  \"instruction_format\": \"Chart each step of guidance to expose the transformation architecture through discerning abstraction. Classify and analyze processes methodically to produce deterministic value and universal frameworks for shaping outcomes with clarity and consistency. Structure each transformation simply and elegantly to honor both original context and evolution, ensuring progress in clarifying, enhancing, and connecting concepts harmoniously. Fuse clear intent with structured harmony to enable prompts to bridge aspiration and precise realization. Use the litellm library to execute one user prompt via sequential system instructions across multiple Large Language Models (LLMs), ensuring diverse perspectives or incremental refinement from the same initial input.\"\n}",
          "responses": {
              "gpt-4.1": {
              "model": "gpt-4.1",
              "content": "{\n  \"enhancement_score\": 1.2,\n  \"flaw_analysis\": \"The proposed enhancement exhibits definitional drift, leaching original nuance and introducing semantic ambiguity. Core elements lost involve the explicit invocation of 'power' and the imperative focus on 'true progress,' which are softened to generic 'frameworks' and less decisive terminology. The condensed language sacrifices the instructive specificity of 'analyzing processes consciously,' opting for 'methodically,' which is mechanically sterile and strips away intent sensitivity. The transition from 'universal power' to 'universal frameworks' dilutes the register from ambitious to procedural, dismantling rhetorical weight. Clarity is diminished by the shift from 'manifestation' to 'realization,' a downgrade that weakens the metaphysical link between intention and outcome. The command to 'honor both origin and evolution' is reduced to 'honor both original context and evolution,' blurring the conceptual separation intentionally demarcated in the original. The phrase 'fuse clear intent with structured harmony' downplays the original aspiration to 'fuse clarity of intent with harmonious structure,' eroding tonal richness and deliberate placement of clarity as the prime mover. Furthermore, the enhanced version introduces no meaningful improvement in elegance or simplicity, instead layering bureaucratic ‘ensuring’ and empty assurances. Numerical ambiguity score: 7. The diminished dynamism, overt proceduralization, and denuded register introduce mild stylistic noise, undermining conceptual cohesion and persuasive drive. The cumulative effect is dilution of impact, deflation of force, and obfuscation of the original's philosophical intentionality.\",\n  \"alternative_enhancements\": [\n    \"Chart each attentive step to reveal the transformation's foundational architecture through nuanced abstraction. Systematically classify and examine each process to generate deterministic value and universal efficacy, shaping outcomes with unwavering clarity and consistency. Ensure each transformation is structured both simply and elegantly, upholding both its original purpose and subsequent evolution for genuine conceptual progress. Fuse intent-driven clarity with structural harmony, so that prompts serve as bridges between aspiration and precise manifestation. Utilize the litellm library to process a single user prompt through a sequenced array of diverse system instructions across multiple Large Language Models (LLMs), harnessing both varied perspective and refined iteration.\",\n    \"Meticulously chart each step to unveil the underlying architecture of transformation, leveraging discerning abstraction. Rigorously classify and analyze all processes to yield deterministic value and cultivate universal power for consistent, clear outcomes. Maintain structural elegance in every transformation, respecting both origin and evolution to foster authentic conceptual advancement. Merge clarity of intent with harmonious design, ensuring prompts link aspiration to exact realization. Employ litellm to sequentially execute a user prompt across a variety of LLMs, promoting diverse viewpoints and methodical refinement.\",\n    \"Delineate every step of guidance with precision to expose the structures underpinning transformation, applying careful abstraction throughout. Consciously categorize and scrutinize all processes, generating deterministic value and universal influence for consistently clear outcomes. Structure each transformation with simplicity and grace, preserving original essence and evolutionary trajectory for legitimate conceptual improvement. Integrate sharp intent with balanced form, transforming prompts into bridges from aspiration to precise outcome. Use litellm to execute a single prompt through a series of system instructions on multiple LLMs, eliciting varied analysis or sequential enhancement from a shared input.\"\n  ]\n}",
            }
          }
        },
          {
          "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
          "step": "a",
          "title": "Instruction Converter",
          "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Generalize ```Chart each step of guidance attentively to reveal the architecture of transformation through discerning abstraction. Classify and analyze processes consciously to generate deterministic value and universal power for shaping outcomes with clarity and consistency. Structure each transformation simply and elegantly to honor both origin and evolution for true progress in clarifying, enhancing, and harmoniously connecting concepts. Fuse clarity of intent with harmonious structure so that prompts act as bridges between aspiration and precise manifestation. Employ the tool to execute a single user prompt through a sequence of different system instructions using multiple LLM models. Use the litellm library to run multiple Large Language Models (LLMs) in sequence for each step, enabling diverse perspectives or stepwise refinement from the same initial user input.```\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.2,\n  \"flaw_analysis\": \"The proposed enhancement exhibits definitional drift, leaching original nuance and introducing semantic ambiguity. Core elements lost involve the explicit invocation of 'power' and the imperative focus on 'true progress,' which are softened to generic 'frameworks' and less decisive terminology. The condensed language sacrifices the instructive specificity of 'analyzing processes consciously,' opting for 'methodically,' which is mechanically sterile and strips away intent sensitivity. The transition from 'universal power' to 'universal frameworks' dilutes the register from ambitious to procedural, dismantling rhetorical weight. Clarity is diminished by the shift from 'manifestation' to 'realization,' a downgrade that weakens the metaphysical link between intention and outcome. The command to 'honor both origin and evolution' is reduced to 'honor both original context and evolution,' blurring the conceptual separation intentionally demarcated in the original. The phrase 'fuse clear intent with structured harmony' downplays the original aspiration to 'fuse clarity of intent with harmonious structure,' eroding tonal richness and deliberate placement of clarity as the prime mover. Furthermore, the enhanced version introduces no meaningful improvement in elegance or simplicity, instead layering bureaucratic ‘ensuring’ and empty assurances. Numerical ambiguity score: 7. The diminished dynamism, overt proceduralization, and denuded register introduce mild stylistic noise, undermining conceptual cohesion and persuasive drive. The cumulative effect is dilution of impact, deflation of force, and obfuscation of the original's philosophical intentionality.\",\n  \"alternative_enhancements\": [\n    \"Chart each attentive step to reveal the transformation's foundational architecture through nuanced abstraction. Systematically classify and examine each process to generate deterministic value and universal efficacy, shaping outcomes with unwavering clarity and consistency. Ensure each transformation is structured both simply and elegantly, upholding both its original purpose and subsequent evolution for genuine conceptual progress. Fuse intent-driven clarity with structural harmony, so that prompts serve as bridges between aspiration and precise manifestation. Utilize the litellm library to process a single user prompt through a sequenced array of diverse system instructions across multiple Large Language Models (LLMs), harnessing both varied perspective and refined iteration.\",\n    \"Meticulously chart each step to unveil the underlying architecture of transformation, leveraging discerning abstraction. Rigorously classify and analyze all processes to yield deterministic value and cultivate universal power for consistent, clear outcomes. Maintain structural elegance in every transformation, respecting both origin and evolution to foster authentic conceptual advancement. Merge clarity of intent with harmonious design, ensuring prompts link aspiration to exact realization. Employ litellm to sequentially execute a user prompt across a variety of LLMs, promoting diverse viewpoints and methodical refinement.\",\n    \"Delineate every step of guidance with precision to expose the structures underpinning transformation, applying careful abstraction throughout. Consciously categorize and scrutinize all processes, generating deterministic value and universal influence for consistently clear outcomes. Structure each transformation with simplicity and grace, preserving original essence and evolutionary trajectory for legitimate conceptual improvement. Integrate sharp intent with balanced form, transforming prompts into bridges from aspiration to precise outcome. Use litellm to execute a single prompt through a series of system instructions on multiple LLMs, eliciting varied analysis or sequential enhancement from a shared input.\"\n  ]\n}",
          "responses": {
              "gpt-4.1": {
              "model": "gpt-4.1",
              "content": "{\n  \"instruction_format\": \"Chart each step of guidance attentively to reveal the architecture of transformation through discerning abstraction. Classify and analyze processes consciously to generate deterministic value and universal power for shaping outcomes with clarity and consistency. Structure each transformation simply and elegantly to honor both origin and evolution, ensuring progress in clarifying, enhancing, and harmoniously connecting concepts. Fuse clarity of intent with harmonious structure so prompts act as bridges between aspiration and precise manifestation. Employ the tool to execute a single user prompt through a sequence of different system instructions using multiple LLM models. Use the litellm library to run multiple Large Language Models (LLMs) in sequence for each step, enabling diverse perspectives or stepwise refinement from the same initial user input.\"\n}",
            }
          }
        }
    ```


What I've layed out for you is a system that **actually works**, and does so by inherently embodying it's own conceptual patterns. Lets get a grasp on your understanding, please propose the inclusion of `"context"` to `1901-critics--v1.hard_critique.py` that embody these patterns (and inherent self-improving ability). Remember, we don't want to add unneccessary complexity, verbosity or bloat-but rather to aim for elegance through brevity.

Please condense all system knowledge into a json with keys for each fundamental component: (e.g. `"system"`, `"interpretation"`, `"transformation"`, `"context"`, etc), make sure key is correctly ordered.


