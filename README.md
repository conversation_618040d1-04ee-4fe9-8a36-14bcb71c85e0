# AI Systems - Instruction Sequence Executor

Multi-step LLM instruction sequence executor with template management system. Execute user prompts through sequences of different system instructions using multiple LLM models for diverse perspectives and step-by-step refinement.

## Features

- Execute prompts through multiple system instructions
- Support for multiple LLM models per step
- Template-based instruction sequences
- Cost tracking and structured JSON output
- Chain mode for sequential processing
- Aggregator sequences for result synthesis

## Quick Start

```bash
# Install dependencies
uv sync

# Run with default sequence
uv run python src/main.py --prompt "Your question here"

# Use specific sequence and models
uv run python src/main.py --sequence "0121" --models "gpt-4o,claude-3-haiku" --prompt "Your question"
```

## Core Components

- **main.py**: Core execution engine with LLM interaction via litellm
- **templates/**: Instruction template management system with stage-based organization
- **Template Catalog**: Dynamic template discovery and sequence management
- **Chain Mode**: Sequential step processing with output chaining
- **Aggregator**: Result synthesis across multiple steps
- **Cost Tracking**: Integrated cost calculation for API usage
- **Streaming Output**: Asynchronous execution with structured JSON results

## Usage Examples

```bash
# Basic usage with default sequence
uv run python src/main.py --prompt "Analyze this text for key insights"

# Specific sequence and multiple models
uv run python src/main.py --sequence "3031" --models "gpt-4o,claude-3-sonnet" --prompt "Your text here"

# Chain mode with aggregation
uv run python src/main.py --sequence "3100:a-c" --chain-mode --aggregator "3022" --prompt "Complex analysis task"

# Using embedded sequence and model specifications
uv run python src/main.py --prompt "[MODEL:gpt-4o|claude-3-haiku] [SEQ:3031|3100:a-c] Transform this text"
```

## Configuration

Set environment variables for API access:
- `OPENAI_API_KEY`
- `ANTHROPIC_API_KEY`
- `OPENROUTER_API_KEY`

---

## 🎯 **Universal Template-Based Instruction Processing**

A revolutionary system that transforms any instruction or prompt into a standardized, machine-parsable, three-part canonical structure that can be executed in sequences and chained together.
