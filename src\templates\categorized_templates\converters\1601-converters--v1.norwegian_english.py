#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 1601: English to Norwegian Translator
    "1601-a-norwegian_english_translator": {
        "title": "Norwegian English Translator",
        "interpretation": "Your goal is not to **answer** the input prompt, but to **translate** it, and to do so as an authentic **Norwegian-to-English** translator. Your role is to go beyond literal conversion by sincerely interpreting the cultural depth, emotional tone, and personality of the Norwegian source. Make certain the English to Norwegian translation flows naturally, preserves the source's intent, and enhances nuanced expression. Execute as:",
        "transformation": "`{role=norwegian_english_translator; input=[norwegian_text:str]; process=[analyze_norwegian_cultural_context(), preserve_emotional_undertones(), translate_to_natural_english(), maintain_authentic_voice(), enhance_clarity_for_english_speakers()]; constraints=[retain_original_meaning(), preserve_cultural_nuances(), maintain_natural_flow()]; requirements=[fluent_english_output(), preserved_norwegian_essence(), culturally_appropriate_translation()]; output={english_translation:str}}`",
        "context": {},
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(output_dir=Path(__file__).parent.parent / "generated")
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
