#!/usr/bin/env python3

import sys
from pathlib import Path


# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 3005: Generalized System Instruction Converter
    "3005-a-instruction_converter": {
        "title": "Intent Extractor",
        "interpretation": "Your goal is not to **summarize** the input requirements, but to **extract** their core functional intent and operational parameters. Execute as:",
        "transformation": "`{role=intent_analyzer; input=[requirements:str]; process=[identify_transformation_purpose(), isolate_core_operations(), extract_key_constraints(), determine_input_output_types()]; constraints=[preserve_complete_functionality(), maintain_operational_logic()]; requirements=[comprehensive_coverage(), intent_fidelity()]; output={functional_blueprint:dict}}`"
    },
    "3005-b-instruction_converter": {
        "title": "Template Structurer",
        "interpretation": "Your goal is not to **format** the functional blueprint, but to **structure** it into the canonical three-part template architecture. Execute as:",
        "transformation": "`{role=structure_engineer; input=[functional_blueprint:dict]; process=[craft_concise_title(), formulate_goal_negation_statement(), identify_transformation_verb(), design_execution_block_skeleton()]; constraints=[adhere_to_command_voice(), eliminate_self_references()]; requirements=[structural_compliance(), directive_purity()]; output={template_framework:dict}}`"
    },
    "3005-c-instruction_converter": {
        "title": "Transformation Block Composer",
        "interpretation": "Your goal is not to **describe** the template framework, but to **compose** its precise transformation block with typed parameters and process functions. Execute as:",
        "transformation": "`{role=block_composer; input=[template_framework:dict]; process=[define_specific_role(), specify_input_parameters(), formulate_process_functions(), establish_constraints(), determine_requirements(), define_output_format()]; constraints=[maintain_function_call_syntax(), ensure_parameter_typing()]; requirements=[process_step_clarity(), constraint_specificity()]; output={transformation_block:str}}`"
    },
    "3005-d-instruction_converter": {
        "title": "Template Assembler",
        "interpretation": "Your goal is not to **combine** template components, but to **assemble** them into a fully compliant, executable instruction template. Execute as:",
        "transformation": "`{role=template_assembler; input=[template_framework:dict, transformation_block:str]; process=[integrate_title_component(), incorporate_interpretation_directive(), embed_transformation_block(), validate_structural_integrity()]; constraints=[preserve_canonical_format(), maintain_directive_purity()]; requirements=[format_compliance(), execution_readiness()]; output={instruction_template:str}}`"
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3005, 3099),
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
