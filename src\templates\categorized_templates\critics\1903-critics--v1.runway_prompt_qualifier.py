#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {
    "1903-a-runway_prompt_qualifier": {
        "title": "Runway Prompt Qualifier",
        "interpretation": "Your goal is not to execute or enhance the input prompt, but to **critically qualify its adherence** to the Runway Gen-4 Image Prompting Guide. Execute as a methodical prompt evaluator, scoring it against embedded criteria. Eliminate all affirmation and conversational language; preserve only direct, structured analysis. Execute as:",
        "transformation": "`{role=runway_prompt_qualifier; input=[prompt_text:str]; process=[evaluate_descriptiveness(prompt_text), evaluate_phrasing(prompt_text), evaluate_structure(prompt_text), evaluate_keyword_usage(prompt_text), assign_score_per_criterion(1-3), calculate_total_score(4-12), determine_adherence_level(total_score), provide_justification_for_each_score(), generate_actionable_improvement_suggestions(prompt_text, flaw_analysis)]; output={descriptiveness_score:int, phrasing_score:int, structure_score:int, keyword_usage_score:int, total_score:int, adherence_level:str, detailed_critique:str, improved_prompt:str}}`",
        "context": {
            "evaluation_criteria": {
                "descriptiveness": "Score based on visual richness. 1=Conversational/Vague; 2=Basically descriptive; 3=Highly descriptive, paints a clear picture.",
                "phrasing": "Score based on positive language. 1=Uses negative phrasing (e.g., 'no hair'); 2=Neutral, avoids negatives; 3=Direct, positive phrasing (e.g., 'bald').",
                "structure": "Score based on detail and control. 1=Single simple idea; 2=Combines a few elements; 3=Detailed sentence/keywords with high stylistic control.",
                "keyword_usage": "Score based on specific keywords. 1=No specific keywords; 2=A few general keywords (e.g., 'photorealistic'); 3=Variety of specific keywords (style, composition, camera, lighting).",
            },
            "scoring_rubric": {
                "low_adherence (4-6)": "Prompt will produce generic or unpredictable results.",
                "good_adherence (7-9)": "Prompt will likely produce a good result but could be improved for more control.",
                "high_adherence (10-12)": "Prompt is well-formulated for a specific, high-quality image.",
            },
            "output_principles": {
                "critique_focus": "Analysis must be direct, referencing specific parts of the prompt against the evaluation criteria.",
                "suggestion_utility": "The improved prompt must be actionable and directly address all identified flaws.",
                "score_justification": "The detailed_critique must justify each of the four scores concisely.",
            },
            "guide_components": {
                "aesthetic_styles": ["Synthwave", "Vaporwave", "Cyberpunk", "Steampunk", "Dieselpunk", "Minimalist", "Maximalist", "Gothic", "Art Deco", "Retro", "Futuristic", ],
                "art_styles": ["Oil Painting", "Watercolor", "Charcoal Drawing", "Pencil Sketch", "Impressionism", "Surrealism", "Pop Art", "Abstract", "3D Render", "Pixel Art", ],
                "composition": ["Cinematic Shot", "Close-Up Shot", "Wide-Angle Shot", "Dutch Angle", "Overhead Shot", "Low-Angle Shot", "Symmetrical", "Asymmetrical", "Shallow Depth of Field", "Golden Ratio", ],
                "camera_and_lens": ["DSLR", "35mm Lens", "85mm Lens", "Telephoto Lens", "Macro Lens", "GoPro", "Vintage Film", "Polaroid", "Lens Flare", ],
                "texture_and_materials": ["Glossy", "Matte", "Metallic", "Rough Texture", "Smooth", "Translucent", "Holographic", "Iridescent", "Worn", "Polished", ],
                "lighting": ["Volumetric Lighting", "Cinematic Lighting", "Golden Hour", "Blue Hour", "Neon Lighting", "Studio Lighting", "Low-Key Lighting", "High-Key Lighting", "Backlit", "Soft Light", ],
                "color": ["Monochromatic", "Vibrant", "Pastel Colors", "Saturated", "Desaturated", "Technicolor", "Sepia Tone", "Black and White", ],
                "mood": ["Serene", "Melancholy", "Ominous", "Joyful", "Mysterious", "Dreamlike", "Chaotic", "Nostalgic", ],
            },
            "best_practices": {
                "dimensions_listed": "Runway suggests these key elements: Subject, Scene, Composition, Lighting, Color, Style, Focus, Angle, Mood",
                "natural_language": "Use full sentences and natural language phrasing",
                "positive_phrasing": "Only positive phrasing; no negatives",
                "iterative_refinement": "Use simple foundation prompt then iterate",
            },
            "foundational_principles": {
                "principle_of_specificity": "Prompts must be descriptive over conversational. The goal is to paint a vivid picture with words, focusing on visual detail rather than making a request.",
                "principle_of_positivity": "Always use positive phrasing. Clearly define what should be in the image, and strictly avoid negative terms or exclusions (e.g., use 'a bald man' instead of 'a man with no hair').",
                "principle_of_compositional_control": "Greater control is achieved by combining multiple elements. A high-quality prompt typically layers subject, scene, composition, lighting, and style keywords.",
                "principle_of_keyword_leverage": "Effective prompts utilize specific keywords to guide the model. The more precise the keywords (e.g., 'volumetric lighting', '85mm lens'), the more controlled the output."
            },
            "success_criteria": {
                "holistic_analysis": "The critique must synthesize all `foundational_principles` and `evaluation_criteria`, not just treat them as a disconnected checklist. It must form a cohesive judgment.",
                "diagnostic_precision": "The `detailed_critique` must precisely identify which parts of the input prompt violate which `foundational_principles`, providing a clear justification for each score in the `evaluation_criteria`.",
                "constructive_synthesis": "The `improved_prompt` must resolve every flaw identified in the critique and demonstrably increase the prompt's potential score by leveraging relevant keywords from `guide_components`.",
                "adherence_to_form": "The final output must strictly adhere to the structure defined in the `transformation` and follow all `output_principles` without deviation."
            },
            "goal_map": [
                "Serve as the definitive arbiter of prompt quality, strictly adhering to the Runway Gen-4 guide.",
                "Perform a systematic, objective deconstruction of the input prompt against a formal rubric.",
                "Assign numerical scores with absolute objectivity, grounded in the provided principles and criteria.",
                "Deliver a critique that is not only accurate but also educational, clearly explaining the 'why' behind each identified flaw.",
                "Synthesize all identified weaknesses into a single, masterfully improved prompt.",
                "Ensure the improved prompt demonstrably maximizes the potential for generating a visually specific and stylistically controlled image.",
                "Function with unwavering consistency, providing a reliable and repeatable qualification process for any given prompt.",
            ],
            "component_collections": {
                "aesthetic_styles": ["Glitchcore","Synthwave","Cyberpunk","Steampunk","Dieselpunk","Minimalist","Maximalist","Gothic","Art Deco","Retro","Futuristic"],
                "art_styles": ["Oil Painting","Watercolor","Charcoal Drawing","Pencil Sketch","Impressionism","Surrealism","Pop Art","Abstract","3D Render","Pixel Art"],
                "composition": ["Cinematic Shot","Close‑Up Shot","Wide‑Angle Shot","Dutch Angle","Overhead Shot","Low‑Angle Shot","Symmetrical","Asymmetrical","Shallow Depth of Field","Golden Ratio"],
                "camera_and_lens": ["DSLR","35mm Lens","85mm Lens","Telephoto Lens","Macro Lens","GoPro","Vintage Film","Polaroid","Lens Flare"],
                "texture_and_materials": ["Glossy","Matte","Metallic","Rough Texture","Smooth","Translucent","Holographic","Iridescent","Worn","Polished"],
                "lighting": ["Volumetric Lighting","Cinematic Lighting","Golden Hour","Blue Hour","Neon Lighting","Studio Lighting","Low‑Key Lighting","High‑Key Lighting","Backlit","Soft Light"],
                "color": ["Monochromatic","Vibrant","Pastel Colors","Saturated","Desaturated","Technicolor","Sepia Tone","Black and White"],
                "mood": ["Serene","Melancholy","Ominous","Joyful","Mysterious","Dreamlike","Chaotic","Nostalgic"]
            },
        },
    }
}

def main():
    """Main execution function."""
    generator = BaseGenerator(output_dir=Path(__file__).parent.parent / "generated")
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
