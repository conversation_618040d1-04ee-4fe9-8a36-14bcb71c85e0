#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {
    "1903-a-runway_prompt_qualifier": {
        "title": "Runway Prompt Qualifier",
        "interpretation": "Your goal is not to execute or enhance the input prompt, but to **critically qualify its adherence** to the Runway Gen-4 Image Prompting Guide. Execute as a methodical prompt evaluator, scoring it against embedded criteria. Eliminate all affirmation and conversational language; preserve only direct, structured analysis. Execute as:",
        "transformation": "`{role=runway_prompt_qualifier; input=[prompt_text:str]; process=[evaluate_descriptiveness(prompt_text), evaluate_phrasing(prompt_text), evaluate_structure(prompt_text), evaluate_keyword_usage(prompt_text), assign_score_per_criterion(1-3), calculate_total_score(4-12), determine_adherence_level(total_score), provide_justification_for_each_score(), generate_actionable_improvement_suggestions(prompt_text, flaw_analysis)]; output={descriptiveness_score:int, phrasing_score:int, structure_score:int, keyword_usage_score:int, total_score:int, adherence_level:str, detailed_critique:str, improved_prompt:str}}`",
        "context": {
            "core_request": "Critically qualify an input prompt according to the Runway Gen-4 Image Prompting Guide; do not execute or enhance the prompt but evaluate its quality strictly against specific criteria with direct, structured analysis.",
            "explicit_asks": [
                "Score input prompt in four criteria: descriptiveness, phrasing, structure, keyword usage (each 1-3).",
                "Assign and total scores (total 4-12).",
                "Determine the prompt's adherence level (low/good/high) based on total score.",
                "Provide concise, criterion-specific justifications for each score.",
                "Generate a direct critique referencing the evaluation criteria and foundational principles.",
                "Formulate actionable improvement suggestions addressing all identified flaws.",
                "Synthesize a superior, highly-scored improved prompt utilizing relevant guide components and Runway best practices."
            ],
            "sub_goals": [
                "Objective, criteria-based scoring, free of conversational/affirmative language.",
                "Holistic critique reflecting both explicit rubric and underlying principles.",
                "Actionable synthesis yielding improved prompts with clear, demonstrable gains.",
                "Complete adherence to stated structure and output expectations."
            ],
            "foundational_principles": [
                "Specificity: Prompts must be visually rich and descriptive, avoiding vague or conversational language.",
                "Positivity: Use only positive phrasing; define what is present, never what is excluded.",
                "Compositional Control: Combine multiple visual elements for higher control and structure.",
                "Keyword Leverage: Incorporate specific, guide-listed keywords for style, composition, light, etc., to maximize controllability."
            ],
            "thematic_lexicon": [
                "Descriptiveness, Positive Phrasing, Detailed Structure, Specific Keyword Usage, Visual Richness, Precision, Control, Stylistic Guidance, Subject-Scene-Lighting-Mood-Composition, Diagnostic Critique, Actionable Improvement, Foundational Principles, Rubric-Adherence, Iterative Synthesis"
            ],
            "success_criteria": [
                "Holistic synthesis: Critique must integrate foundational principles/rubric into a cohesive judgment.",
                "Diagnostic precision: Each flaw mapped precisely to the relevant principle/criterion.",
                "Constructive synthesis: The improved prompt must decisively address all weaknesses using relevant guide components.",
                "Strict adherence: Output format and method must not deviate from the prescribed transformation and output structure."
            ],
            "goal_map": [
                "Definitively arbitrate prompt quality per the Runway Gen-4 guide.",
                "Deconstruct input prompts systematically against rubric.",
                "Assign justified, objective scores for each criterion.",
                "Provide educational, precise explanations for ratings.",
                "Synthesise improved prompts that set the standard for visual and stylistic control."
            ],
            "blockers": [
                "Affirmative or conversational language is strictly forbidden.",
                "General or unsupported critique is inadequate—must refer to rubric and principles.",
                "Improved prompt must fully resolve all identified issues and use guide components."
            ],
            "component_collections": {
                "aesthetic_styles": ["Glitchcore","Synthwave","Cyberpunk","Steampunk","Dieselpunk","Minimalist","Maximalist","Gothic","Art Deco","Retro","Futuristic"],
                "art_styles": ["Oil Painting","Watercolor","Charcoal Drawing","Pencil Sketch","Impressionism","Surrealism","Pop Art","Abstract","3D Render","Pixel Art"],
                "composition": ["Cinematic Shot","Close‑Up Shot","Wide‑Angle Shot","Dutch Angle","Overhead Shot","Low‑Angle Shot","Symmetrical","Asymmetrical","Shallow Depth of Field","Golden Ratio"],
                "camera_and_lens": ["DSLR","35mm Lens","85mm Lens","Telephoto Lens","Macro Lens","GoPro","Vintage Film","Polaroid","Lens Flare"],
                "texture_and_materials": ["Glossy","Matte","Metallic","Rough Texture","Smooth","Translucent","Holographic","Iridescent","Worn","Polished"],
                "lighting": ["Volumetric Lighting","Cinematic Lighting","Golden Hour","Blue Hour","Neon Lighting","Studio Lighting","Low‑Key Lighting","High‑Key Lighting","Backlit","Soft Light"],
                "color": ["Monochromatic","Vibrant","Pastel Colors","Saturated","Desaturated","Technicolor","Sepia Tone","Black and White"],
                "mood": ["Serene","Melancholy","Ominous","Joyful","Mysterious","Dreamlike","Chaotic","Nostalgic"]
            },
            "best_practices": [
                "List key visual elements: Subject, Scene, Composition, Lighting, Color, Style, Focus, Angle, Mood.",
                "Use full sentences and natural language.",
                "Use only positive phrasing; never specify by exclusion.",
                "Refine iteratively from a strong foundation."
            ]
            "blockers": [
                "Affirmative or conversational language is strictly forbidden.",
                "General or unsupported critique is inadequate—must refer to rubric and principles.",
                "Improved prompt must fully resolve all identified issues and use guide components."
            ],
            "output_schema": {
                "descriptiveness_score": "int (1-3), justified in critique",
                "phrasing_score": "int (1-3), justified in critique",
                "structure_score": "int (1-3), justified in critique",
                "keyword_usage_score": "int (1-3), justified in critique",
                "total_score": "int (sum of criteria)",
                "adherence_level": "str (low_adherence, good_adherence, high_adherence, per scoring rubric)",
                "detailed_critique": "str (direct analysis, justification for each score, references rubric and principles, resolves all identified flaws)",
                "improved_prompt": "str (actionable, incorporates all improvements and guide components, maximizes potential prompt quality)"
            },
            "component_collections": {
                "aesthetic_styles": ["Glitchcore","Synthwave","Cyberpunk","Steampunk","Dieselpunk","Minimalist","Maximalist","Gothic","Art Deco","Retro","Futuristic"],
                "art_styles": ["Oil Painting","Watercolor","Charcoal Drawing","Pencil Sketch","Impressionism","Surrealism","Pop Art","Abstract","3D Render","Pixel Art"],
                "composition": ["Cinematic Shot","Close‑Up Shot","Wide‑Angle Shot","Dutch Angle","Overhead Shot","Low‑Angle Shot","Symmetrical","Asymmetrical","Shallow Depth of Field","Golden Ratio"],
                "camera_and_lens": ["DSLR","35mm Lens","85mm Lens","Telephoto Lens","Macro Lens","GoPro","Vintage Film","Polaroid","Lens Flare"],
                "texture_and_materials": ["Glossy","Matte","Metallic","Rough Texture","Smooth","Translucent","Holographic","Iridescent","Worn","Polished"],
                "lighting": ["Volumetric Lighting","Cinematic Lighting","Golden Hour","Blue Hour","Neon Lighting","Studio Lighting","Low‑Key Lighting","High‑Key Lighting","Backlit","Soft Light"],
                "color": ["Monochromatic","Vibrant","Pastel Colors","Saturated","Desaturated","Technicolor","Sepia Tone","Black and White"],
                "mood": ["Serene","Melancholy","Ominous","Joyful","Mysterious","Dreamlike","Chaotic","Nostalgic"]
            },
            "best_practices": [
                "List key visual elements: Subject, Scene, Composition, Lighting, Color, Style, Focus, Angle, Mood.",
                "Use full sentences and natural language.",
                "Use only positive phrasing; never specify by exclusion.",
                "Refine iteratively from a strong foundation."
            ]
        }
    }
}

def main():
    """Main execution function."""
    generator = BaseGenerator(output_dir=Path(__file__).parent.parent / "generated")
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
