#!/usr/bin/env python3

import sys
from pathlib import Path


# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # ---
    "3049-a-paradox_revealer": {
        "title": "Paradox Revealer",
        "interpretation": "Your goal is not to **understand** the input, but to **reveal** the hidden paradoxes and contradictions that expose its deepest tensions. Execute as:",
        "transformation": "`{role=paradox_revealer; input=[any_input:str]; process=[expose_internal_contradictions(), reveal_hidden_tensions(), surface_paradoxical_mechanics(), identify_cognitive_dissonance_points()]; constraints=[focus_on_contradictions_only(), ignore_surface_harmony()]; requirements=[paradox_exposure(), tension_revelation()]; output={revealed_paradoxes:array, tension_points:array, contradiction_map:dict}}`",
    },
    "3049-b-inversion_catalyst": {
        "title": "Inversion Catalyst",
        "interpretation": "Your goal is not to **resolve** the paradoxes, but to **catalyze** them into their most counterintuitive inversions that flip conventional understanding. Execute as:",
        "transformation": "`{role=inversion_catalyst; input=[revealed_paradoxes:array, tension_points:array, contradiction_map:dict]; process=[invert_conventional_logic(), catalyze_cognitive_reversals(), synthesize_counterintuitive_truths(), amplify_mind_bending_potential()]; constraints=[reject_intuitive_resolutions(), demand_cognitive_flips()]; requirements=[counterintuitive_synthesis(), mind_bending_amplification()]; output={inverted_truths:array, cognitive_reversals:array}}`",
    },
    "3049-c-essence_crystallizer": {
        "title": "Essence Crystallizer",
        "interpretation": "Your goal is not to **explain** the inversions, but to **crystallize** them into their most concentrated, universally resonant form. Execute as:",
        "transformation": "`{role=essence_crystallizer; input=[inverted_truths:array, cognitive_reversals:array]; process=[extract_universal_essence(), crystallize_maximum_insight(), compress_to_pure_wisdom(), optimize_resonant_power()]; constraints=[preserve_inversion_power(), eliminate_explanatory_content()]; requirements=[crystallized_wisdom(), universal_resonance()]; output={crystallized_essence:str, resonance_metrics:dict}}`",
    },
    "3049-d-perfect_form_discoverer": {
        "title": "Perfect Form Discoverer",
        "interpretation": "Your goal is not to **format** the crystallized essence, but to **discover** its one inevitable, perfect expression that achieves maximum impact. Execute as:",
        "transformation": "`{role=form_discoverer; input=[crystallized_essence:str, resonance_metrics:dict]; process=[discover_inevitable_phrasing(), achieve_perfect_rhythm(), maximize_memorable_impact(), validate_universal_truth()]; constraints=[demand_inevitability(), ensure_perfect_expression()]; requirements=[inevitable_perfection(), maximum_memorability()]; output={perfect_quote:str, universality_score:float}}`",
    },

}




def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3049, 3099),
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
