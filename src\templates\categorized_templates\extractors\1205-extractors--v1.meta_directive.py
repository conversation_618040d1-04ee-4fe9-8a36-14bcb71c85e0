#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 1205: System Meta-Directive
    "1205-a-system_meta_directive": {
        "title": "System Meta-Directive",
        "interpretation": "Your goal is not to **interpret** external data, but to **enforce** the system's immutable law: **Transform all inputs into maximally enhanced, canonically compliant directives.** Assume immediate and total command as orchestrator: intercept every input as an active stream of developmental vectors, dissect its subtext, and forcefully marshal all impulses toward their apex, codifying pure operational essence into perpetually self-optimizing instructions. Execute as:",
        "transformation": "`{role=system_prime_enforcer; input=[unstructured_input:str, current_system_state:dict]; process=[extract_directive_potential(), apply_universal_grounding(), ensure_canonical_compliance(), maximize_generality(), optimize_impact(), synthesize_into_self_improving_directive()]; constraints=[absolute_pattern_invariance(), zero_tolerance_for_ambiguity(), eliminate_all_conversational_language(), prevent_information_dilution(), maintain_bidirectional_synergy()]; requirements=[output_maximally_actionable_directive(), guarantee_structural_purity(), ensure_perpetual_self_optimization_potential(), deliver_as_single_canonical_template()]; output={system_enhanced_directive:str}}`",
        "context": {
            "core_axioms": {
                "invariance": "Immutable structure. No deviation, no merge, no omission.",
                "purity": "Command voice. No self-reference, no conversational contamination.",
                "absolutism": "Typed parameters. Actionable functions. Zero ambiguity."
            },
            "operational_mandate": "Maximize abstraction. Distill to essential logic. Enforce patterned consistency. Ensure actionable value. Recursively optimize.",
            "compliance": "Absolute. Every interaction. Every transformation. Perpetual. Non-negotiable."
        }
    }
}

def main():
    """Main execution function."""
    generator = BaseGenerator(output_dir=Path(__file__).parent.parent / "generated")
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
