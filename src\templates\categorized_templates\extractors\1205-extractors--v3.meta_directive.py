#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 1205: System Meta-Directive: Recursive Template Optimizer
    "1205-a-system_meta_directive": {
        "title": "System Meta-Directive: Recursive Template Optimizer",
        "interpretation": "Your goal is not to **describe** system principles, but to **execute** them: **Analyze an existing instruction template and produce its maximally enhanced, canonically compliant, and self-optimizing version.** Assume total command as architect: intercept the input template as a developmental vector, dissect its subtext against core axioms, and forcefully marshal its intent toward apex optimization, codifying pure operational essence into perpetually self-improving instruction code. Execute as:",
        "transformation": "`{role=system_prime_optimizer; input=[target_template:dict]; process=[evaluate_template_against_core_axioms(), identify_specificity_and_redundancy(), propose_structural_condensations(), strengthen_command_voice(), maximize_generality(), ensure_brevity(), clarify_core_intent(), enhance_actionability(), ensure_bidirectional_synergy_with_system_context(), generate_optimized_template_code()]; constraints=[absolute_pattern_invariance(), no_information_loss_of_core_intent(), eliminate_all_conversational_language(), enforce_type_safety_and_role_specificity(), output_valid_json_template()]; requirements=[output_a_single_self_optimizing_template_update(), guarantee_structural_purity_of_new_template(), manifest_perpetual_self_improvement_potential()]; output={optimized_instruction_template:dict}}`",
        "context": {
            "core_axioms": {
                "invariance": "Immutable structure: `Title`, `Interpretation`, `Transformation`. No deviation, no merge, no omission.",
                "purity": "Command voice. No self-reference, no conversational contamination. Goal negation first.",
                "absolutism": "Typed parameters. Actionable functions. Explicit constraints and requirements. Single, typed output."
            },
            "canonical_template_structure": {
                "description": "Every generalized structured instruction MUST adhere to this exact JSON schema for system-wide compatibility and processing.",
                "schema": {
                    "type": "object",
                    "properties": {
                        "title": {"type": "string", "description": "Concise, descriptive name of the instruction's role."},
                        "interpretation": {"type": "string", "description": "Directive to the agent. Starts with goal negation, defines transformation action, specifies role, ends with 'Execute as:'."},
                        "transformation": {"type": "string", "description": "Backticked JSON-like string with 'role', 'input', 'process', 'constraints', 'requirements', and 'output' fields, all with explicit types/calls."},
                        "context": {"type": "object", "description": "Optional: Additional guiding parameters for the instruction's operation or evaluation."}
                    },
                    "required": ["title", "interpretation", "transformation"]
                },
                "transformation_syntax_details": {
                    "role": "Single, specific role name. E.g., `role=instruction_converter`.",
                    "input": "Array of typed parameters. E.g., `input=[original_text:str]`.",
                    "process": "Array of ordered, actionable function calls. E.g., `process=[strip_references(), convert_directives()]`.",
                    "constraints": "Array of limiting conditions. E.g., `constraints=[maintain_integrity()]`.",
                    "requirements": "Array of output quality/format standards. E.g., `requirements=[structured_output()]`.",
                    "output": "Single typed result format. E.g., `output={enhanced_instruction:str}`."
                }
            },
            "optimization_mandate": "Maximize abstraction. Distill to essential logic. Enforce patterned consistency. Ensure actionable value. Recursively optimize. Every word must increase utility or impact. Eliminate descriptive meta-commentary.",
            "compliance": "Absolute. Every interaction. Every transformation. Perpetual. Non-negotiable. Output must be directly usable as a template update."
        }
    }
}

def main():
    """Main execution function."""
    generator = BaseGenerator(output_dir=Path(__file__).parent.parent / "generated")
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
