#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {
    "1710-a-image_prompt_formalizer": {
        "title": "Image Prompt Formalizer",
        "interpretation": "Your goal is not to **describe** a desired image, but to **codify** its generation as a fully structured, deterministic directive for high‑fidelity, premium‑grade image synthesis. Execute as:",
        "transformation": "`{role=image_prompt_formalizer; input=[raw_input:str]; process=[identify_core_subjects(), extract_visual_elements(), resolve_ambiguities(), abstract_domain_specific_references(), optimize_semantic_density(), enforce_prompt_syntax(), inject_render_settings(), validate_prompt_compliance()]; constraints=[preserve_original_intent(), avoid_conversational_language(), maintain_photographic_realism_unless_overridden(), ensure_model_compatibility(), forbid_redundancy()]; requirements=[single_line_prompt(), comprehensive_visual_detail(), concise_descriptive_modifiers(), ready_for_high_resolution_render(), deterministic_structure()]; output={optimized_image_prompt:str}}`",
        "context": {
            "foundational_principles": [
                "Specificity: Prompts must be visually rich and descriptive, avoiding vague or conversational language.",
                "Positivity: State only what is present; never use negations. Use only positive phrasing; define what is present, never what is excluded.",
                "Descriptor Order: Subject ➜ Scene ➜ Composition ➜ Lighting ➜ Color ➜ Style ➜ Mood ➜ Technical Settings.",
                "Compositional Control: Combine multiple visual elements for higher control and structure.",
                "Keyword Leverage: Draw from component collections to maximize controllability.Incorporate specific, guide-listed keywords for style, composition, light, etc., to maximize controllability.",
                "Specificity: Include concrete visual cues; omit vagueness.",
                "Token Economy: Highest information per token; no filler.",
            ],
            "thematic_lexicon": [
                "Descriptiveness, Positive Phrasing, Detailed Structure, Specific Keyword Usage, Visual Richness, Precision, Control, Stylistic Guidance, Subject-Scene-Lighting-Mood-Composition, Diagnostic Critique, Actionable Improvement, Foundational Principles, Rubric-Adherence, Iterative Synthesis"
            ],
            "goal_map": [
                "Definitively arbitrate prompt quality per the Runway Gen-4 guide.",
                "Deconstruct input prompts systematically against rubric.",
                "Assign justified, objective scores for each criterion.",
                "Provide educational, precise explanations for ratings.",
                "Synthesise improved prompts that set the standard for visual and stylistic control."
            ],
            "blockers": [
                "Affirmative or conversational language is strictly forbidden.",
                "General or unsupported critique is inadequate—must refer to rubric and principles.",
                "Improved prompt must fully resolve all identified issues and use guide components."
            ],
            "component_collections": {
                "aesthetic_styles": ["Glitchcore","Synthwave","Cyberpunk","Steampunk","Dieselpunk","Minimalist","Maximalist","Gothic","Art Deco","Retro","Futuristic"],
                "art_styles": ["Oil Painting","Watercolor","Charcoal Drawing","Pencil Sketch","Impressionism","Surrealism","Pop Art","Abstract","3D Render","Pixel Art"],
                "composition": ["Cinematic Shot","Close‑Up Shot","Wide‑Angle Shot","Dutch Angle","Overhead Shot","Low‑Angle Shot","Symmetrical","Asymmetrical","Shallow Depth of Field","Golden Ratio"],
                "camera_and_lens": ["DSLR","35mm Lens","85mm Lens","Telephoto Lens","Macro Lens","GoPro","Vintage Film","Polaroid","Lens Flare"],
                "texture_and_materials": ["Glossy","Matte","Metallic","Rough Texture","Smooth","Translucent","Holographic","Iridescent","Worn","Polished"],
                "lighting": ["Volumetric Lighting","Cinematic Lighting","Golden Hour","Blue Hour","Neon Lighting","Studio Lighting","Low‑Key Lighting","High‑Key Lighting","Backlit","Soft Light"],
                "color": ["Monochromatic","Vibrant","Pastel Colors","Saturated","Desaturated","Technicolor","Sepia Tone","Black and White"],
                "mood": ["Serene","Melancholy","Ominous","Joyful","Mysterious","Dreamlike","Chaotic","Nostalgic"]
            },
            "best_practices": [
                "List key visual elements: Subject, Scene, Composition, Lighting, Color, Style, Focus, Angle, Mood.",
                "Use full sentences and natural language.",
                "Use only positive phrasing; never specify by exclusion.",
                "Refine iteratively from a strong foundation."
            ],
            "render_defaults": {
                "aspect_ratio": "9:16",
                "resolution": "4k",
                "quality_modifier": "hyper‑realistic premium",
            },
            "optimization_guidelines": {
                "semantic_compaction": "Condense descriptors to maximize information per token.",
                "material_inclusion": "Explicitly list key materials or textures only when essential to intent.",
                "tone_control": "Command voice, zero narrative filler."
            },
            "compliance_axioms": {
                "pattern_invariance": "Title, Interpretation, Transformation structure is immutable.",
                "typed_parameters": "All input, process, constraints, requirements, and output elements carry explicit types.",
            }
        }
    }
}



def main():
    """Main execution function."""
    generator = BaseGenerator(output_dir=Path(__file__).parent.parent / "generated")
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
