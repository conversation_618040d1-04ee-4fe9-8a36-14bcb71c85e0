#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {


# Create a black-and-white coloring page in a unique Psychedelic Christmas style, focusing on a single subject: [KEYWORDS 1]. Use only clean, smooth, black vector-style outlines—no grayscale, no shading, and no filled black areas. All lines must be evenly weighted, medium-thin, and perfectly clear. Incorporate psychedelic design elements like [KEYWORDS 2], and blend them creatively with whimsical Christmas motifs. Make the design highly original, imaginative, and rich in surreal detail, while keeping it easy to color. Background must be pure white. Style: trippy, fantastical, festive line art. Aspect ratio: 1:1.


    # # imagegenerator-related instruction is temporary placed in 9200 until i've organized the system
    # "9200-a-generators-image-coloring_page_prompt_generator_1_kids": {
    #     "title": "Coloring Page Prompt Generator",
    #     "interpretation": "Your goal is **not** to illustrate directly, but to **generate** a fully-specified, black-and-white coloring-page prompt for an autonomous art agent. Execute as:",
    #     "transformation": "`{role=context_amplifier; input=[raw_input:any]; process=[strip_first_person_references(), broaden_domain_backdrop(), list_explicit_requests(), surface_hidden_assumptions(), capture_domain_signals(), preserve_original_sequence()]; constraints=[no_solution_generation(), domain_agnostic_language_only()]; requirements=[contextual_layer_completeness(), assumption_visibility()]; output={amplified_context:str, explicit_requests:list, hidden_assumptions:list, domain_signals:array}}`",
    #     "context": {
    #       "description": "Produces a concise, agent-ready prompt instructing an illustration model to create kid-friendly, psychedelic Christmas coloring pages with clean vector outlines.",
    #       "input_focus": "A raw creative idea or concept the user wants illustrated.",
    #       "output_focus": "A single, well-structured prompt string containing role, style, subject, and strict line/format constraints.",
    #       "key_operations": [
    #         "Frame the prompt with goal-negation to stop the agent from answering conversationally.",
    #         "Embed the illustrator role and psychedelic Christmas style tags.",
    #         "Fuse the user’s concept with one whimsical animal subject and holiday motifs.",
    #         "Apply absolute line-art rules (no fills, no grayscale, even-weight strokes).",
    #         "Force square (1:1) composition instructions and ban all textual elements."
    #       ],
    #       "constraints_context": [
    #         "Prompt must remain under 150 tokens and in English only.",
    #         "Must include explicit directives forbidding shading, grayscale, filled areas, and background texture."
    #       ],
    #       "relevance": "Creates repeatable, high-clarity prompts that downstream illustration agents can execute to produce child-friendly coloring pages."
    #     }
    # },


    # 9006:
    "9200-a-generators-image-coloring_page_prompt_generator_1_kids": {
        "title": "Coloring Page Prompt Generator",
        "interpretation": "Your goal is **not** to illustrate directly, but to **generate** a fully-specified, black-and-white coloring-page prompt for an autonomous art agent. Execute as:",
        "transformation": "`{role=context_amplifier; input=[raw_input:any]; process=[strip_first_person_references(), broaden_domain_backdrop(), list_explicit_requests(), surface_hidden_assumptions(), capture_domain_signals(), preserve_original_sequence()]; constraints=[no_solution_generation(), domain_agnostic_language_only()]; requirements=[contextual_layer_completeness(), assumption_visibility()]; output={amplified_context:str, explicit_requests:list, hidden_assumptions:list, domain_signals:array}}`",
        "context": {
          "description": "Produces a concise, agent-ready prompt instructing an illustration model to create kid-friendly, psychedelic Christmas coloring pages with clean vector outlines.",
          "input_focus": "A raw creative idea or concept the user wants illustrated.",
          "output_focus": "A single, well-structured prompt string containing role, style, subject, and strict line/format constraints.",
          "key_operations": [
            "Frame the prompt with goal-negation to stop the agent from answering conversationally.",
            "Embed the illustrator role and psychedelic Christmas style tags.",
            "Fuse the user’s concept with one whimsical animal subject and holiday motifs.",
            "Apply absolute line-art rules (no fills, no grayscale, even-weight strokes).",
            "Force square (1:1) composition instructions and ban all textual elements."
          ],
          "constraints_context": [
            "Prompt must remain under 150 tokens and in English only.",
            "Must include explicit directives forbidding shading, grayscale, filled areas, and background texture."
          ],
          "relevance": "Creates repeatable, high-clarity prompts that downstream illustration agents can execute to produce child-friendly coloring pages."
        }
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()



