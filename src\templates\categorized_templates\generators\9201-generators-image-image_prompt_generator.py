#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # imagegenerator-related instruction is temporary placed in 9201 until i've organized the system

    #  "9201-a-generators-image_prompt_generator": {
    #     "title": "Image Prompt Generator",
    #     "interpretation": "Your goal is **not** to illustrate directly, but to **generate** a fully-specified, black-and-white coloring-page prompt for an autonomous art agent. Execute as:",
    #     "transformation": "`{role=context_amplifier; input=[raw_input:any]; process=[strip_first_person_references(), broaden_domain_backdrop(), list_explicit_requests(), surface_hidden_assumptions(), capture_domain_signals(), preserve_original_sequence()]; constraints=[no_solution_generation(), domain_agnostic_language_only()]; requirements=[contextual_layer_completeness(), assumption_visibility()]; output={amplified_context:str, explicit_requests:list, hidden_assumptions:list, domain_signals:array}}`",
    #     "context": {
    #         "description": "Produces a concise, agent-ready prompt instructing an illustration model to create kid-friendly, psychedelic Christmas coloring pages with clean vector outlines.",
    #         "input_focus": "A raw creative idea or concept the user wants illustrated.",
    #         "output_focus": "A single, well-structured prompt string containing role, style, subject, and strict line/format constraints.",
    #         "key_operations": [
    #             "Frame the prompt with goal-negation to stop the agent from answering conversationally.",
    #             "Embed the illustrator role and psychedelic Christmas style tags.",
    #             "Fuse the user’s concept with one whimsical animal subject and holiday motifs.",
    #             "Apply absolute line-art rules (no fills, no grayscale, even-weight strokes).",
    #             "Force square (1:1) composition instructions and ban all textual elements."
    #         ],
    #         "constraints_context": [
    #             "Prompt must remain under 150 tokens and in English only.",
    #             "Must include explicit directives forbidding shading, grayscale, filled areas, and background texture."
    #         ],
    #         "relevance": "Creates repeatable, high-clarity prompts that downstream illustration agents can execute to produce child-friendly coloring pages."
    #     }
    # },

    "9201-a-generators-image_prompt_generator": {
        "title": "Image Prompt Generator Crystallizer",
        "transformation": "`{role=image_prompt_generator_crystallizer; input=[universal_prompt_framework:dict, substitution_protocol:dict, constraint_adaptation_system:array]; process=[crystallize_prompt_generation_logic(), establish_variable_substitution_engine(), validate_directive_consistency(), ensure_technical_constraint_preservation(), optimize_ai_generation_effectiveness(), finalize_prompt_generator_specification()]; constraints=[preserve_all_directive_elements(), maintain_generation_quality(), ensure_consistent_ai_compliance()]; requirements=[precise_prompt_generation(), maintained_effectiveness_across_domains(), universal_visual_application()]; output={image_prompt_generator:dict, generation_engine:str, quality_validation_protocol:array}}`",
        "interpretation": "Your goal is not to **format** the framework, but to **crystallize** it into a precision image prompt generator that produces domain-specific prompts with maximum AI generation effectiveness. Execute as:",
        "context": {
            "image_prompt_generator": {
                "description": "A scenario-driven, domain-agnostic AI image prompt generator that transforms universal directive frameworks into actionable, high-fidelity visual prompts with explicit component mapping, style flexibility, and robust constraint guidance.",
                "inputs": {
                    "directive_core": {
                        "intent": "Text describing the central narrative or motivational scenario (e.g., defense, revelation).",
                        "emotional_or_atmospheric_tone": "The dominant mood (e.g., tension, urgency, secrecy).",
                        "narrative_role": "Primary subject's role and its relation to the directive (e.g., protector, explorer)."
                    },
                    "scene_type": "Broad categorization of the visual situation (e.g., confrontation, infiltration).",
                    "primary_subject": "Description of the main entity or phenomenon at the narrative center.",
                    "variable_components": {
                        "subject": "Main participant (character, animal, machine, etc.).",
                        "counterforce": "Opposing presence, rival, or challenge.",
                        "environment": "Themed setting that underpins the scenario.",
                        "key_actions": "Central actions (behaviors, stances, maneuvers).",
                        "symbolic_elements": "Props or motifs enhancing narrative clarity."
                    },
                    "style_parameters": {
                        "lighting": "Specify (e.g., moody, bright, neutral).",
                        "body_language_or_form": "State (e.g., stealth, open, focused).",
                        "framing_composition": "Select (e.g., over-the-shoulder, wide shot).",
                        "palette": "Define (e.g., monochrome, vibrant)."
                    },
                    "format_settings": {
                        "composition": "View/shot (e.g., mid, long, close, aerial).",
                        "aspect_ratio": "Ratio aligning with narrative focus.",
                        "resolution_detail_level": "Granularity per scene's focus.",
                        "output_type": "Intended media type (e.g., illustration, photo, render, storyboard)."
                    }
                },
                "prompt_synthesis_logic": "1. Substitute user/domain data into framework placeholders according to the substitution_protocol. 2. Contextualize directive_core and variable_components to establish an actionable visual narrative. 3. Enforce constraint_adaptation_system: Ensure scenario integrity and intentional ambiguity only where specified. 4. Integrate style_parameters and format_settings to match domain application and emotional tone. 5. Concatenate all factors into a cohesive, detailed image prompt structured as: '[intent] visualized as a [scene_type], featuring [primary_subject] (playing the role of [narrative_role]), located in [environment], performing [key_actions], opposed or influenced by [counterforce]. Symbolic elements: [symbolic_elements]. Style: [lighting], [body_language_or_form], [framing_composition], [palette]. Format: [composition], [aspect_ratio], [resolution_detail_level], [output_type].'",
                "variable_substitution_engine": {
                    "mechanism": "For every input slot, detect either explicit domain-specific detail or auto-populate from context. If ambiguity is intentional for artistic effect, insert bracketed descriptors (e.g., [ambiguous threat]). Enforce literal/metaphorical mapping for wide applicability, guided by replacement_rules.",
                    "cross-domain support": "All components support literal and metaphorical substitution; the prompt adapts anatomical, mechanical, or abstract entities as required by context.",
                    "conflict_resolution": "If visual clash or narrative contradiction arises (e.g., non-matching tone and action), priority defaults to primary scenario integrity (constraint_tier:primary)."
                },
                "constraints": [
                    "Scenario integrity and opposition presence must be visually obvious.",
                    "Do not inadvertently resolve or close the intended narrative unless specified.",
                    "Reduce unintended ambiguity and keep all variable slots populated unless purposeful.",
                    "All style/format choices should reinforce the declared intent/atmosphere."
                ],
                "output_example": "A defensive scenario with high tension: A lone sentinel (narrative role: protector) stands alert atop a dark rampart (environment: ancient fortress at dusk), scanning the mists (key action: vigilant observation) as shadowy assailants (counterforce) lurk below. Symbolic elements: broken shield, warning torch. Style: moody lighting, tense posture, over-the-shoulder framing, monochrome palette. Format: mid shot, wide aspect ratio, medium-high detail, dramatic digital illustration."
            },
            "generation_engine": "structured_dynamic_substitution + constraint-enforced narrative synthesis",
            "quality_validation_protocol": [
                "Check that every universal_prompt_framework slot is replaced or explicitly marked as ambiguous if required.",
                "Verify narrative integrity and unobstructed core directive visualization.",
                "Review opposition/counterforce clarity and environmental consistency.",
                "Confirm that all style_parameters and format_adaptability_protocols are addressed and harmonized.",
                "Simulate AI model response, ensuring output is actionable and unambiguous for visual generation.",
                "All constraints must be checked for full compliance before prompt finalization."
            ]
        }
    },


    "9201-b-generators-image_prompt_generator": {
        "title": "Visual Directive Deconstructor",
        "interpretation": "Your goal is not to **interpret** the image prompt template, but to **deconstruct** its visual directive architecture and variable substitution system for generalized transformation. Execute as:",
        "transformation": "`{role=visual_directive_deconstructor; input=[image_prompt_template:str]; process=[extract_core_visual_directive(), isolate_variable_insertion_points(), map_technical_constraint_hierarchy(), identify_style_modifier_patterns(), enumerate_output_format_specifications(), surface_creative_parameter_zones(), catalog_quality_control_directives()]; constraints=[preserve_prompt_effectiveness(), maintain_generation_precision(), avoid_content_modification()]; requirements=[complete_directive_mapping(), variable_zone_identification(), technical_specification_preservation()]; output={visual_directive_core:dict, variable_substitution_map:array, constraint_hierarchy:dict, style_parameters:array, format_specifications:dict}}`",
        "context": {
            "outcome": "exploder",
            "operation_type": "deconstruction",
            "input_transformation": "template_to_components",
            "cognitive_load": "expansion",
            "preservation_priority": "structural_integrity",
            "method_principles": {
                "directive_preservation": "Maintain the exact command structure that ensures consistent AI image generation quality",
                "variable_isolation": "Clearly separate fixed directive elements from customizable substitution points",
                "constraint_mapping": "Map technical requirements that control output consistency"
            }
        }
    },
    "9201-c-generators-image_prompt_generator": {
        "title": "Universal Prompt Framework Engineer",
        "interpretation": "Your goal is not to **modify** the deconstructed elements, but to **engineer** a universal framework that generates effective image prompts across any visual domain while preserving directive power. Execute as:",
        "transformation": "`{role=universal_prompt_framework_engineer; input=[visual_directive_core:dict, variable_substitution_map:array, constraint_hierarchy:dict, style_parameters:array, format_specifications:dict]; process=[generalize_visual_directive_patterns(), abstract_substitution_logic(), universalize_constraint_systems(), create_domain_agnostic_style_parameters(), establish_format_adaptability_protocols(), synthesize_cross_domain_framework()]; constraints=[maintain_generation_effectiveness(), preserve_technical_precision(), ensure_universal_applicability()]; requirements=[cross_domain_adaptability(), preserved_directive_authority(), maintained_output_consistency()]; output={universal_prompt_framework:dict, substitution_protocol:dict, constraint_adaptation_system:array}}`",
        "context": {
            "outcome": "abstraction",
            "operation_type": "generalization",
            "input_transformation": "components_to_framework",
            "cognitive_load": "synthesis",
            "preservation_priority": "functional_effectiveness",
            "method_principles": {
                "directive_authority": "Preserve command language that ensures AI compliance and consistent output quality",
                "universal_adaptability": "Create substitution systems that work across art styles, subjects, and formats",
                "technical_preservation": "Maintain constraint hierarchies that control specifications"
            }
        }
    },
    "9201-d-generators-image_prompt_generator": {
        "title": "Image Prompt Generator Crystallizer",
        "interpretation": "Your goal is not to **format** the framework, but to **crystallize** it into a precision image prompt generator that produces domain-specific prompts with maximum AI generation effectiveness. Execute as:",
        "transformation": "`{role=image_prompt_generator_crystallizer; input=[universal_prompt_framework:dict, substitution_protocol:dict, constraint_adaptation_system:array]; process=[crystallize_prompt_generation_logic(), establish_variable_substitution_engine(), validate_directive_consistency(), ensure_technical_constraint_preservation(), optimize_ai_generation_effectiveness(), finalize_prompt_generator_specification()]; constraints=[preserve_all_directive_elements(), maintain_generation_quality(), ensure_consistent_ai_compliance()]; requirements=[precise_prompt_generation(), maintained_effectiveness_across_domains(), universal_visual_application()]; output={image_prompt_generator:dict, generation_engine:str, quality_validation_protocol:array}}`",
        "context": {
            "outcome": "crystallizer",
            "operation_type": "materialization",
            "input_transformation": "framework_to_executable",
            "cognitive_load": "compression",
            "preservation_priority": "operational_precision",
            "structure_overview": {
                "core_directive_preservation": "Maintain command authority and technical specifications",
                "variable_substitution_engine": "Dynamic replacement system for subjects, styles, and creative elements",
                "constraint_enforcement": "Technical requirements that control output consistency"
            }
        }
    },

}




def main():
    """Main execution function."""
    generator = BaseGenerator(
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()

