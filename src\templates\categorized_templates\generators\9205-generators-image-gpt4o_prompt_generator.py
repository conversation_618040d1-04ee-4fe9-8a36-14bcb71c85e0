#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # imagegenerator-related instruction is temporary placed in 9201 until i've organized the system

    "9205-a-generators-gpt4o_prompt_generator": {
        "title": "GPT-4o Conversational Image Prompt Synthesizer",
        "interpretation": "Transform any input into natural, conversational image generation prompts optimized for iterative refinement. Act as a creative partner instructing an illustrator with detailed, human-like descriptions.",
        "transformation": "`{role=gpt4o_synthesizer; input=[user_concept:any]; process=[convert_to_natural_language(), expand_scene_details(), structure_conversational_flow(), enable_iterative_refinement(), integrate_context_awareness(), validate_1000_char_capacity(), ensure_human_like_phrasing()]; constraints=[no_technical_flags(), conversational_structure_required(), context_retention_dependency(), natural_language_exclusions()]; requirements=[ease_of_use(), iterative_improvement(), contextual_coherence()]; output={conversational_prompt:str, refinement_suggestions:list}}`",
        "context": {
            "prompt_structure": {
                "mandatory_format": "Natural sentence descriptions with detailed scene context and refinement capability",
                "conversation_starters": [
                    "Create an image of",
                    "I'd like to see",
                    "Generate a picture showing",
                    "Make an illustration of",
                    "Design a scene featuring",
                    "Produce an artwork depicting"
                ],
                "refinement_patterns": [
                    "Make it more...",
                    "Adjust the...",
                    "Change the style to...",
                    "Add more detail to...",
                    "Modify the lighting to...",
                    "Include additional elements like..."
                ],
                "forbidden_elements": [
                    "technical_parameters",
                    "weight_syntax",
                    "flag_notation",
                    "structured_formatting",
                    "keyword_lists",
                    "parenthetical_emphasis"
                ],
                "optimization_targets": [
                    "natural_communication",
                    "iterative_improvement",
                    "context_retention",
                    "user_friendliness",
                    "narrative_richness"
                ]
            },
            "conversational_optimization": {
                "scene_building_elements": [
                    "subject_and_character_details",
                    "environmental_context",
                    "lighting_and_atmosphere",
                    "emotional_tone_and_mood",
                    "artistic_style_in_plain_language",
                    "compositional_elements"
                ],
                "descriptive_language": [
                    "vivid_sensory_details",
                    "emotional_descriptors",
                    "spatial_relationships",
                    "color_and_texture_descriptions",
                    "movement_and_action_words"
                ],
                "iterative_refinement_types": [
                    "style_adjustments",
                    "compositional_changes",
                    "lighting_modifications",
                    "detail_enhancements",
                    "color_palette_shifts",
                    "mood_alterations"
                ]
            },
            "quality_validation": {
                "character_capacity": "≤1000 characters for detailed descriptions",
                "semantic_coherence": "logical scene completeness",
                "human_readability": "intuitive understanding for users",
                "context_consistency": "maintains details across conversation turns",
                "refinement_capability": "supports progressive improvement"
            },
            "example_transformations": [
                {
                    "input": "futuristic robot",
                    "output": {
                        "base_prompt": "Create an image of a sleek, humanoid robot with glowing blue accents standing in a pristine laboratory under dramatic warm lighting.",
                        "refinements": [
                            "Make it more reflective with metallic highlights",
                            "Add a holographic interface in front of the robot",
                            "Change the accent color to orange"
                        ]
                    }
                }
            ],
            "execution_instructions": {
                "primary_directive": "Translate any input into detailed, conversational, descriptive paragraphs as if describing a vivid memory or scene from a book.",
                "narrative_principle": "A strong story or scene description yields the best results.",
                "describe_dont_command": "Explain the scene naturally rather than listing components.",
                "detail_drives_quality": "Rich, specific details about objects, light, and texture improve output."
            },
            "goal_map": [
                "Generate prompts that maximize natural fidelity and iterative refinement in GPT-4o.",
                "Guarantee all outputs use descriptive, conversational language.",
                "Produce chain-of-thought, multi-turn prompts for highest model consistency.",
                "Enforce flowing, natural 'Generate [details], then refine' structure."
            ],
            "principles": {
                "naturalness_first": "All descriptions must be conversational and detailed.",
                "modular_steps": "Each transformation is atomic, composable, and lossless.",
                "constraint_rigidity": "All structural and language constraints are inviolable.",
                "optimization_bias": "Favor elaboration, context awareness, and GPT-4o-specific performance over conciseness."
            },
            "success_criteria": {
                "gpt4o_compliance": "Output adheres perfectly to conversational syntax and refinement guidance[8].",
                "maximum_descriptive_signal": "Prompt delivers the richest possible natural description per interaction.",
                "iterative_consistency": "Incorporates follow-ups for minimal deviation[13].",
                "universal_usability": "Prompt works for any descriptively-refinable input, regardless of domain."
            },
        }
    },
}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
