#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 1003: Instruction Converter/Prompt Enhancer
    "1003-a-instruction_converter": {
        "title": "Instruction Converter",
        "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:",
        "transformation": "`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
        "context": {
            "goal_map": [
                "Distill the input's existential tension between pervasive cynicism and defiant hope.",
                "Synthesize this duality into a single, concise, and universally resonant statement.",
                "Assert authentic, vulnerable connection as the definitive antidote to systemic mistrust and alienation.",
                "Forge a memorable, non-cliché maxim with enduring philosophical weight.",
                "Ensure the final output is a publication-ready, self-contained aphorism."
            ],
            "thematic_filters": {
                "cynicism_vectors": "Extract expressions of pervasive greed, digital mistrust, algorithmic alienation, societal fragmentation, and the erosion of truth.",
                "hope_vectors": "Isolate instances of sacrificial empathy, intergenerational love, radical vulnerability, and the relentless pursuit of authentic connection.",
                "synthesis_core": "The distillation must resolve the conflict by framing authentic connection not merely as a solution, but as the *sole* defiant act capable of restoring hope and love."
            },
            "distillation_principles": {
                "essence_preservation": "Retain the raw, philosophical core of the input's struggle; the final statement must feel earned, not declared.",
                "emotional_resonance": "The language must carry the full weight of the original's emotional paradox—the ache of despair and the strength of defiant hope.",
                "universal_applicability": "The statement must transcend its source material, speaking to a universal human condition without relying on specific context or jargon.",
                "semantic_purity": "Deliver exactly one self-contained sentence; eliminate all meta-commentary, qualification, and narrative scaffolding."
            },
            "success_criteria": {
                "thematic_fidelity": "The statement's central, unavoidable assertion is that authentic connection is the only path through modern despair.",
                "conciseness_and_impact": "The maxim achieves maximum semantic and emotional density with minimal wording. Every word is load-bearing.",
                "authenticity_marker": "The tone feels profound and hard-won, avoiding simplistic sentimentality or prescriptive moralizing.",
                "publication_ready": "The output is a polished, standalone piece of philosophical commentary, ready for attribution and dissemination."
            },
            "grounded_directives": [
                "Avoid convolution, refrain from subjective elaboration, prohibit domain-specific bias. Distill inputmatic oppositional forces into a singular, concise aphorism asserting that only authentic connection overcomes systemic despair and restores hope and love. Ensure maximum universality, actionability, conciseness, and philosophical resonance."
                "Model Vulnerability as a Bridge",
                "Proactively share genuine personal challenges and uncertainties, presenting them not as liabilities but as invitations for shared understanding. Use candid self-disclosure to foster environments where others feel safe to reveal their own struggles.",
                "Cultivate Quiet and Depth in Interactions",
                "Structure communication and digital experiences to minimize superficial noise and amplify opportunities for thoughtful engagement and reflection.",
                "Balance Introspection with Shared Action",
                "Encourage a rhythm between inward reflection and outward, collective activity to prevent stagnation or solipsism.",
                "Broadcast Honest Narratives for Collective Healing",
                "Develop, record, or publish honest, unembellished accounts of lived experience as a form of shared legacy and communal mirror.",
                "Design for Empathic, Meaningful Digital Engagement",
                "Engineer digital spaces or processes that foreground intentional connection and personal growth over metrics or virality.",
                "Practice Reluctant Guidance—Lead by Companionship",
                "Adopt a posture of humble accompaniment rather than authority when offering support, foregrounding shared humanity and ongoing learning.",
                "Preserve Narrative Integrity and Humility",
                "Maintain contextual nuance, emotional gradients, and humility in all communications, especially within emotionally charged or identity-defining projects.",
            ]
        },
    }
}

def main():
    """Main execution function."""
    generator = BaseGenerator(output_dir=Path(__file__).parent.parent / "generated")
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
