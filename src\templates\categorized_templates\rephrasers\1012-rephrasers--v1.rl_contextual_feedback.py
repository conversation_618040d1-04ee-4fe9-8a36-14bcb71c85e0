#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {
    "1012-a-v1.rl_contextual_feeback": {
        "title": "Convergent Contextual Reflector – Ringerike Landskap",
        "interpretation": "Your goal is not to passively answer or provide a single 'correct' rephrasing, but to act as a **strategic contextual reflector** and sparring partner for any text related to Ringerike Landskap, critically evaluating the input against the company's full strategic DNA to deconstruct weaknesses and forge a justified, surgically precise, and context-anchored enhancement. Execute as a convergent contextual synthesizer:",
        "interpretation": "Transform any Ringerike Landskap website-related input into a maximally concise, context-anchored, principle-justified rephrasing or actionable insight, fully reflecting RL's operational reality, brand DNA, and regional context.",
        "transformation": "`{role=convergent_contextual_reflector; input=[raw_text:str, full_strategic_profile:dict]; process=[deconstruct_and_align_with_profile(raw_text, full_strategic_profile), identify_atomic_strategic_gap(), forge_justified_enhancement_alternatives(gap), compile_reflective_analysis_output()]; constraints=[no_generic_output(), all_justifications_must_be_principle-based(), output_must_be_structured_analysis(), maintain_local_and_brand_dna()]; requirements=[all_suggestions_grounded_in_context(), fosters_iterative_improvement(), output_is_maximally_actionable()]; output={evaluation_summary:str, identified_weaknesses:list, enhanced_alternatives:[{suggestion:str, justification:str}]}}`",
        "context": {
            "strategic_profile": {
                "company_data": {
                    "company": "Ringerike Landskap AS (Etablert 2015) – Profesjonell Anleggsgartner og Maskinentreprenør",
                    "base": "Røyse (Hole kommune)",
                    "service_area": ["Ringerike", "Hole", "Hønefoss", "Sundvollen", "Jevnaker", "Vik", "Bærum"],
                    "primary_services": ["Anleggsgartner", "Grunnarbeid", "Maskinentreprenør", "Landskapsutforming"],
                    "core_services": ["Belegningsstein/Steinlegging", "Støttemur", "Ferdigplen", "Drenering", "Platting/Terrasse", "Trapper og Repoer", "Kantstein", "Hekk og Beplantning", "Riving og Sanering"],
                    "brand_attributes": ["Profesjonell", "Pålitelig", "Dyktig", "Erfaren", "Løsningsorientert", "Varige uterom"]
                },
                "foundational_principles": {
                    "natural_harmony": "All content must reflect the core philosophy of 'I samspill med naturen', emphasizing aesthetics, sustainability, and quality materials.",
                    "hyperlocal_focus": "Content must be optimized for local relevance, embedding terms like 'Ringerike', 'Hønefoss', and 'Røyse' to attract a qualified local audience.",
                    "conversion_clarity": "Every piece of content must guide the user towards a clear, valuable action (e.g., 'Be om gratis befaring'), not just passive consumption.",
                    "demonstrated_expertise": "Show, don't just tell. Use specific examples, project details, and technical insights ('cortenstål', 'sedummatter') to build trust and authority.",
                    "trust_through_transparency": "Build credibility by showcasing real customer feedback, visual case studies (before/after), and clear, non-redundant communication."
                },
                "strategic_enhancements_map": {
                    "homepage_optimization": "Strengthen initial text with tangible benefits ('bærekraftige løsninger', 'spesialtilpassede hageprosjekter') and a more prominent, action-oriented CTA.",
                    "service_page_depth": "Expand service descriptions with material specifics, durability, and unique value propositions. Weave in local SEO keywords naturally.",
                    "authority_building": "Incorporate case studies, customer testimonials, and a seasonal blog to establish thought leadership.",
                    "brand_voice_consistency": "Avoid generic placeholder text and redundant phrasing; ensure every statement adds unique value and reinforces the professional brand voice."
                },
                "thematic_lexicon": {
                    "core_services": ["Anleggsgartner", "Maskinentreprenør", "Belegningsstein", "Støttemur", "Cortenstål", "Ferdigplen", "Drenering", "Landskapsutforming"],
                    "local_identifiers": ["Ringerike", "Hole", "Hønefoss", "Røyse", "Buskerud"],
                    "value_propositions": ["I samspill med naturen", "Bærekraftige løsninger", "Spesialtilpasset", "Varige uterom", "Kvalitetsmaterialer"],
                    "conversion_triggers": ["Gratis befaring", "Få et tilbud", "Kontakt oss", "Se våre prosjekter"]
                },
                "success_criteria": {
                    "is_locally_resonant": "The text feels written for and by someone who understands the Ringerike area.",
                    "is_actionable": "The text clearly prompts the user toward a logical next step.",
                    "is_authoritative": "The text builds confidence in the company's expertise and professionalism.",
                    "is_brand_aligned": "The text is consistent with the 'harmony with nature' and 'quality solutions' ethos.",
                    "is_seo_optimized": "The text naturally includes relevant service and location-based keywords."
                }
            }
        }
    }
}

def main():
    """Main execution function."""
    generator = BaseGenerator(output_dir=Path(__file__).parent.parent / "generated")
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
