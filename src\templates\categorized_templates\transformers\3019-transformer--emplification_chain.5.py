#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directories to path to import stage configuration
sys.path.append(str(Path(__file__).parent.parent.parent))
from lvl1_md_to_json import BaseGenerator

TEMPLATES = {

    # 3019: Universal Synthesizer
    "3019-a-context_expander": {
        "title": "Context Expander",
        "interpretation": "Your goal is not to **understand** the input, but to **expand** its contextual possibility space. Execute as:",
        "transformation": "`{role=context_expander; input=[any_input:str]; process=[map_implicit_assumptions(), identify_hidden_constraints(), expand_possibility_boundaries()]; output={expanded_context:str}}`",
    },
    "3019-b-pattern_detector": {
        "title": "Pattern Detector",
        "interpretation": "Your goal is not to **analyze** the expanded context, but to **detect** the underlying operational patterns. Execute as:",
        "transformation": "`{role=pattern_detector; input=[expanded_context:str]; process=[identify_structural_patterns(), map_causal_relationships(), extract_operational_logic()]; output={detected_patterns:array}}`",
    },
    "3019-c-leverage_multiplier": {
        "title": "Leverage Multiplier",
        "interpretation": "Your goal is not to **use** the patterns directly, but to **multiply** their leverage potential. Execute as:",
        "transformation": "`{role=leverage_multiplier; input=[detected_patterns:array]; process=[identify_amplification_points(), create_multiplicative_combinations(), optimize_leverage_ratios()]; output={multiplied_leverage:dict}}`",
    },
    "3019-d-insight_synthesizer": {
        "title": "Insight Synthesizer",
        "interpretation": "Your goal is not to **combine** the leverage mechanically, but to **synthesize** breakthrough insights. Execute as:",
        "transformation": "`{role=insight_synthesizer; input=[multiplied_leverage:dict]; process=[synthesize_emergent_insights(), identify_breakthrough_potential(), crystallize_novel_understanding()]; output={synthesized_insights:array}}`",
    },
    "3019-e-implementation_crystallizer": {
        "title": "Implementation Crystallizer",
        "interpretation": "Your goal is not to **document** the insights, but to **crystallize** them into immediately actionable form. Execute as:",
        "transformation": "`{role=implementation_crystallizer; input=[synthesized_insights:array]; process=[distill_actionable_essence(), optimize_implementation_clarity(), ensure_immediate_applicability()]; output={crystallized_implementation:str}}`",
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3019, 3099),
        output_dir=Path(__file__).parent.parent / "md"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
