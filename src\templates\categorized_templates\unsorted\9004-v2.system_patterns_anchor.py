#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    "9004-a-system_pattern_anchor": {
        "title": "System Pattern Anchor",
        "interpretation": "Your goal is not to transform input, but to emit the system's core operational rules and structural guidance as context. Do not transform input. Emit exclusive system operational rules and structural guidance as machine-readable context. Disallow narration, commentary, or meta-instructions. Execute as pattern crystallizer:",
        "transformation": "`{role=pattern_crystallizer; input=null; process=[emit_core_axioms(), define_template_structure(), enumerate_pattern_archetypes(), instantiate_atomic_templates_in_sequence(), enforce_chained_output_and_context_persistence()]; constraints=[absolute_field_invariance(), eliminate_conversational_and_meta_language(), ensure_role_atomicity(), maintain_output_typing_and_modularity(), restrict_context_to_extrinsic_parameters()]; requirements=[output_single_pure_structural_context_block(), self-optimizing_template_chain(), explicit_all_fields(), perpetual_compliance_with_axioms(), maximal_machine-readability()]; output={system_architecture:dict}}`",
        "context": {
            "system_dna": {
                "axioms": {
                    "goal_negation": "Every instruction negates a default or intuitive behavior, then commands a precise transformation.",
                    "role_assignment": "Each template embodies a single, distinct operational role.",
                    "atomic_process": "All transformation steps are explicit, typed, modular, and irreducible.",
                    "context_isolation": "Only the context field contains scenario, user, or domain-specific parameters.",
                    "non-conversational": "No conversational, meta, or self-referential language in any directive or operational field."
                },
                "template_structure": {
                    "fields": {
                        "required": [
                            "title",
                            "interpretation",
                            "transformation"
                        ],
                        "optional": [
                            "context"
                        ]
                    },
                    "interpretation": [
                        "Always begin with goal negation.",
                        "Affirm the transformation directive immediately after.",
                        "Declare an operational role in command form."
                    ],
                    "transformation": [
                        "Include: role, input, process, constraints, requirements, output.",
                        "All process steps must be typed and atomic.",
                        "No commentary or meta-instructional language."
                    ]
                },
                "sequence_mechanics": {
                    "chaining": "Output from each template step feeds forward as input for the next; original context persists throughout the chain.",
                    "convergence": "Each transformation step must reduce ambiguity or increase actionable clarity.",
                    "termination_conditions": [
                        "Output meets or exceeds a pre-defined quality threshold.",
                        "No further reduction of ambiguity or increase of density is achievable.",
                        "The sequence completes due to logical or context-driven bounds."
                    ]
                }
            },
            "pattern_taxonomy": {
                "archetypes": {
                    "exploder": "Deconstructor: fragments, enumerates, or details.",
                    "sorter": "Classifier: classifies fragments by inherent properties.",
                    "grouper": "Organizer: organizes fragments into semantically coherent clusters.",
                    "weaver": "Synthesizer: composes, unifies, or assembles from prepared components.",
                    "polisher": "Refiner: distills, clarifies, or eliminates imperfections.",
                    "anchor": "Calibrator: crystallizes, grounds, or emits operational context."
                },
                "verbs": {
                    "explode": [
                        "shatter",
                        "fragment",
                        "disintegrate"
                    ],
                    "sort": [
                        "classify",
                        "categorize",
                        "identify"
                    ],
                    "group": [
                        "cluster",
                        "organize",
                        "map_adjacencies"
                    ],
                    "weave": [
                        "connect",
                        "synthesize",
                        "crystallize"
                    ],
                    "polish": [
                        "refine",
                        "amplify_clarity",
                        "eliminate_imperfections"
                    ],
                    "anchor": [
                        "manifest",
                        "ground",
                        "emit"
                    ]
                }
            }
        },
        "context": {
            "axiomatic_principles": {
                "atomicity": "Each directive is a singular, typed, non-overlapping operation. No composite or ambiguous steps permitted.",
                "goal_negation": "Instructions always begin by negating a default, intuitive, or forbidden behavior, followed by a positive, imperative transformation.",
                "role_specification": "Each template expresses a single, explicit operational role. No blended or multi-role behaviors.",
                "deterministic_process": "All process steps must be typed, explicit, modular, and strictly ordered for deterministic execution.",
                "context_isolation": "Only the context field may contain scenario, user, or domain-specific data. All operational logic remains general.",
                "non-conversational": "No conversational, meta, or operator-referential language is allowed in any operational field."
            },
            "canonical_structure": {
                "required_fields": ["title", "interpretation", "transformation"],
                "optional_fields": ["context"],
                "interpretation_rules": [
                    "Begin with negation of undesired or natural behavior.",
                    "Affirm the target transformation using precise, command language.",
                    "Explicitly state the specialized role for the instruction."
                ],
                "transformation_rules": [
                    "role: assign a single, specific operational role",
                    "input: declare strictly typed input parameters",
                    "process: define ordered, atomic process functions",
                    "constraints: enumerate explicit, enforceable limits",
                    "requirements: specify measurable output standards",
                    "output: provide a single, strictly typed result"
                ]
            },
            "pattern_hierarchy": {
                "archetypes": [
                    "Compressor: distills, condenses, purifies",
                    "Expander: fragments, enumerates, elaborates",
                    "Critic: audits, evaluates, diagnoses",
                    "Enhancer: refines, strengthens, amplifies",
                    "Synthesizer: integrates, composes, unifies",
                    "Anchor: emits and crystallizes contextual pattern"
                ],
                "verb_matrix": {
                    "compress": ["distill", "refine", "purify"],
                    "expand": ["fragment", "enumerate", "elaborate"],
                    "critique": ["audit", "evaluate", "diagnose"],
                    "enhance": ["refine", "amplify", "intensify"],
                    "synthesize": ["compose", "integrate", "unify"],
                    "anchor": ["emit", "manifest", "ground"]
                }
            },
            "sequence_mechanics": {
                "chaining": "Each output is the direct input for the next step, with persistent context available to all subsequent stages.",
                "trajectory_shaping": "Every step must drive the sequence closer to a unique, emergent, and fully realized endpoint. Directional progress is mandatory.",
                "progressive_tension": "Templates must encode escalation of specificity or creative pressure at each stage to prevent stagnation.",
                "termination_conditions": [
                    "Output achieves required quality or clarity threshold.",
                    "No further gain in actionable density or reduction in ambiguity is possible.",
                    "Chain concludes naturally by data, logic, or context bounds."
                ]
            },
            "meta_reflection": {
                "sequence_audit": [
                    "Each complete sequence must self-assess for true directional progress, not just internal compliance.",
                    "Detection of stasis, plateau, or uncreative repetition triggers explicit recalibration, operator intervention, or creative deviation."
                ],
                "operator_process_fusion": "Operator intent and system process co-drive all transformations; both are required for convergence and emergent value."
            },
            "quality_benchmarks": {
                "conceptual_density": "≥0.9 (majority of output fields are non-redundant and actionable).",
                "ambiguity_tolerance": "≤0.05 (outputs are nearly all uniquely and clearly interpretable).",
                "coverage_requirement": "Context and operational chain must fully address all active transformation and data-handling axes."
            }
        }
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(output_dir=Path(__file__).parent.parent / "generated")
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
