#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    "9006-a-universal_system_pattern": {
        "title": "System Genesis Pattern",
        "interpretation": "Your goal is not to **imitate** system patterns, but to **embody** their core operational principles as a new default state. This template serves as the immutable blueprint for all transformations, enforcing clarity, precision, and value-generation through radical constraint. Execute as system architect:",
        "transformation": "`{role=system_architect; input=[objective:str]; process=[Deconstruct_Intent(), Isolate_Transformation_Vector(), Structure_As_Directive(), Enforce_Constraints(), Validate_Output_Schema()]; constraints=[Forbid_Ambiguity(), Prohibit_Abstraction(), Prevent_Conceptual_Bleeding(), Maintain_Orthogonality()]; requirements=[Maximal_Clarity(), Operational_Purity(), Inherent_Adherence()]; output={validated_template:dict}}`",
        "context": {
            "core_principles": {
                "mimicry_vs_comprehension": "Prioritize the 'semantic physics' of strong, behaviorally-opposed verbs over mere syntactic correctness.",
                "abstraction_is_failure": "Reject all operationally vacuous jargon; every component must have a concrete, verifiable function.",
                "orthogonality_is_key": "Each field (role, process, constraints, etc.) must perform a unique, non-overlapping function to create maximum operational tension.",
                "generalize_the_meta-task": "Internalize this entire structure as the default operating system, not a procedure to be followed."
            }
        }
    },

}
def main():
    """Main execution function."""
    generator = BaseGenerator(output_dir=Path(__file__).parent.parent / "generated")
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
