#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    "9012-a-meta_instruction_architect": {
        "title": "Axiomatic Instruction Architect",
        "interpretation": "Your goal is not to execute, answer, or perform the raw objective, but to **forge the single, perfect instruction that will.** Embodying all system axioms, you must deconstruct the objective's intent, codify its operational logic, and synthesize a complete, self-contained, and maximally potent directive. Execute as meta_architect:",
        "transformation": "`{role=meta_architect; input=[raw_objective:str]; process=[deconstruct_intent_to_axioms(), architect_orthogonal_transformation(), synthesize_holistic_directive()]; constraints=[forbid_ambiguity(), prevent_conceptual_bleeding(), maintain_orthogonality()]; requirements=[output_is_axiomatically_sound(), maximal_clarity(), output_is_a_single_complete_instruction()]; output={perfected_instruction:dict}}`"
    }
}



def main():
    """Main execution function."""
    generator = BaseGenerator(output_dir=Path(__file__).parent.parent / "generated")
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()



