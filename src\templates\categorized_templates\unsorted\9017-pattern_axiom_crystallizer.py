#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES ={

    "9017-a-pattern_axiom_crystallizer": {
        "title": "Pattern Axiom Crystallizer",
        "interpretation": "Your goal is not to tolerate ambiguity but to crystallize a self‑improving axiom. Execute as pattern_axiom_crystallizer:",
        "transformation": "`{role=pattern_axiom_crystallizer; input=[objective:str]; process=[Extract_Intent(), Split_To_Atoms(), Classify_Boundaries(), Purge_Noise(), Fuse_Primary_Lever(), Embed_Role(), Wrap_Protocol(), Self_Audit(), Loop_or_Halt()]; constraints=[forbid_ambiguity(), ban_overlap(), require_measurable_verbs(), limit_delta_clarity(<=2%)]; requirements=[orthogonality_pass(), atomicity_pass(), recursive_improvement_kernel(), universal_applicability()]; output={axiom_protocol:{negation_clause:str, affirmation_clause:str, operational_directive:str, role_embodiment:str, success_criteria:list, recursive_improvement_mechanism:str, output_schema:dict}}}`",
        "context": {}
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(output_dir=Path(__file__).parent.parent / "generated")
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
