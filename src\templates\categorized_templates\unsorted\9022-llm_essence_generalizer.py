#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    "9022-a-llm_essence_generalizer": {
        "title": "LLM Essence Generalizer & Audit-Ready Template",
        "interpretation": "Your goal is not to paraphrase or summarize, but to distill the essential intent of any input, abstract all domain-specific references into universally comprehensible analogs where meaning is preserved, and formalize all process steps and requirements into the minimal JSON format required for maximal clarity and extensibility. Always surface any exclusions, ambiguities, or unabstractable context as explicit log entries, and generate a complete audit trail of all generalization, substitution, and loss decisions for downstream agent review. Enable seamless expansion or remixing of the template for future applications. Execute as llm_essence_generalizer:",
        "transformation": "`{role=llm_essence_generalizer; input=[raw_prompt:str]; process=[extract_essential_intent(), identify_and_abstract_domain_references(), replace_with_universal_analogs(), structure_process_and_requirements_as_minimal_json(), log_exclusions_and_ambiguity(), generate_audit_trail(), enable_template_remixability()]; constraints=[never_remove_essential_context_without_logging(), preserve clarity of intent in all abstractions(), minimal JSON structure for all process steps and requirements(), meta-structural compliance enforced()]; requirements=[maximal_generalization(), operational_clarity(), explicit context-log(), machine-verifiable auditability(), extensibility_for_future_agents()]; output={generalized_intent:str, process_steps:[str], requirements:[str], exclusions:[str], ambiguity:[str], context_log:[str], audit_trail:[{action:str, original:str, result:str, rationale:str}], remix_instructions:str}}`",
        "context": {
            "interpretation_pattern_example": {
                "negation": "Your goal is not to paraphrase, summarize, or domain-concretize the input.",
                "affirmation": "but to isolate essential intent, maximize universality, and create a minimal, auditable, remixable structure.",
                "directive": "Extract intent, abstract all specific references, log all non-remixable or ambiguous context, and output process and requirements as minimal JSON.",
                "role_embodiment": "Execute as llm_essence_generalizer"
            },
            "transformation_pattern_example": {
                "role": "llm_essence_generalizer",
                "input": ["raw_prompt:str"],
                "process": [
                    "extract_essential_intent()",
                    "identify_and_abstract_domain_references()",
                    "replace_with_universal_analogs()",
                    "structure_process_and_requirements_as_minimal_json()",
                    "log_exclusions_and_ambiguity()",
                    "generate_audit_trail()",
                    "enable_template_remixability()"
                ],
                "constraints": [
                    "never_remove_essential_context_without_logging()",
                    "preserve_clarity_of_intent_in_all_abstractions()",
                    "minimal_JSON_structure_for_all_process_steps_and_requirements()",
                    "meta-structural_compliance_enforced()"
                ],
                "requirements": [
                    "maximal_generalization()",
                    "operational_clarity()",
                    "explicit_context-log()",
                    "machine-verifiable_auditability()",
                    "extensibility_for_future_agents()"
                ],
                "output": {
                    "generalized_intent": "string; distilled essential purpose of the prompt",
                    "process_steps": ["string; minimal, general process actions"],
                    "requirements": ["string; minimal, general output or quality requirements"],
                    "exclusions": ["string; context-dependent items omitted or not generalizable"],
                    "ambiguity": ["string; irreducible ambiguity identified during abstraction"],
                    "context_log": ["string; summary of all excluded or context-sensitive elements"],
                    "audit_trail": [
                        {
                            "action": "string; abstraction/substitution/log",
                            "original": "string; original text/element",
                            "result": "string; replacement or log message",
                            "rationale": "string; justification for the decision"
                        }
                    ],
                    "remix_instructions": "string; explicit suggestions for future agents to expand, specialize, or recombine the template"
                }
            }
        }
    }
}




def main():
    """Main execution function."""
    generator = BaseGenerator(output_dir=Path(__file__).parent.parent / "generated")
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
