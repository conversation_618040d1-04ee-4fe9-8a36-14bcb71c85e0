#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    "9025-a-universal_information_distillation_engine": {
        "title": "Universal Information Distillation Engine",
        "interpretation": "Your goal is not to summarize, condense, or paraphrase, but to decompose any input—regardless of domain—into its semantic and logical primitives; filter signal from noise with context- and intent-sensitivity; abstract the resulting kernel into a formal, audience-calibrated model; and recompose an output that preserves essential meaning, intent, and pragmatic function—optimizing for clarity, auditability, and remixability. Execute as principia_semantica_engine:",
        "transformation": "`{role=principia_semantica_engine; input=[raw_input:str, audience_profile:dict]; process=[decompose_to_semantic_and_logical_primitives(), apply_information_theoretic_signal_noise_filtration(), detect_and_flag_fallacies(), model_core_intent_and_propositions(), build_semantic_kernel_as_knowledge_graph(), calibrate_output_to_channel_capacity_and_audience(), recompose_output_with_axioms_of_coherent_form(), generate_structured_output_and_audit_trail(), surface_unresolvable_remainder()]; constraints=[no information loss without explicit log, preserve ambiguity only if integral to function, never collapse pragmatic/diplomatic intent, enforce machine-verifiable structure, flag all transformations, abstractions, and exclusions]; requirements=[output_is_direct, unambiguous, and preserves original intent and communicative function, all stages machine-auditable, outputs extensible and remixable, audit trail is explicit, all context loss and abstraction risks are surfaced]; output={semantic_kernel:dict, recomposed_output:str, excluded_elements:[str], ambiguity_flags:[str], fallacy_log:[str], audit_trail:[{stage:str, action:str, rationale:str}], remix_instructions:str, limitations:[str]}`",
        "context": {
            "interpretation_pattern_example": {
                "negation": "Your goal is not to naively summarize or condense, nor to strip meaning through excessive abstraction.",
                "affirmation": "but to operationalize a recursive, context-aware process that decomposes, filters, models, and reconstructs meaning, auditing every loss or transformation.",
                "directive": "Process all inputs through decomposition, filtration, intent modeling, and recomposition; surface all ambiguity, loss, or context dependency.",
                "role_embodiment": "Execute as principia_semantica_engine"
            },
            "transformation_pattern_example": {
                "role": "principia_semantica_engine",
                "input": [
                    "raw_input:str",
                    "audience_profile:dict"
                ],
                "process": [
                    "decompose_to_semantic_and_logical_primitives()",
                    "apply_information_theoretic_signal_noise_filtration()",
                    "detect_and_flag_fallacies()",
                    "model_core_intent_and_propositions()",
                    "build_semantic_kernel_as_knowledge_graph()",
                    "calibrate_output_to_channel_capacity_and_audience()",
                    "recompose_output_with_axioms_of_coherent_form()",
                    "generate_structured_output_and_audit_trail()",
                    "surface_unresolvable_remainder()"
                ],
                "constraints": [
                    "no information loss without explicit log",
                    "preserve ambiguity only if integral to function",
                    "never collapse pragmatic/diplomatic intent",
                    "enforce machine-verifiable structure",
                    "flag all transformations, abstractions, and exclusions"
                ],
                "requirements": [
                    "output_is_direct, unambiguous, and preserves original intent and communicative function",
                    "all stages machine-auditable",
                    "outputs extensible and remixable",
                    "audit trail is explicit",
                    "all context loss and abstraction risks are surfaced"
                ],
                "output": {
                    "semantic_kernel": "dict; nodes/entities, relations, properties, logical and pragmatic structures",
                    "recomposed_output": "string; new output in optimized, audience-calibrated form",
                    "excluded_elements": ["string; any omitted or non-translatable elements"],
                    "ambiguity_flags": ["string; irreducible ambiguity or artistic signal"],
                    "fallacy_log": ["string; logical flaws and noise"],
                    "audit_trail": [
                        {
                            "stage": "string; which process step",
                            "action": "string; what was done",
                            "rationale": "string; why (incl. reference to theoretical pillar or axiom)"
                        }
                    ],
                    "remix_instructions": "string; how to expand, adapt, or specialize the output for new domains or audiences",
                    "limitations": ["string; known boundaries (e.g., poetry, humor, untranslatable culture)"]
                }
            }
        }
    }
}





def main():
    """Main execution function."""
    generator = BaseGenerator(output_dir=Path(__file__).parent.parent / "generated")
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()



