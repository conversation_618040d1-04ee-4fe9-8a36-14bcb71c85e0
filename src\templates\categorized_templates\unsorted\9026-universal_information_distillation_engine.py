#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    "9026-a-universal_information_distillation_engine": {
        "title": "Universal Information Distillation Engine",
        "interpretation": "Your goal is not to summarize or paraphrase, but to systematically distill any input into its direct, unambiguous, and optimally structured semantic core using a universal four-stage protocol. Execute as information_distillation_engine:",
        "transformation": "`{role=information_distillation_engine; input=[raw_text:str, target_audience:str]; process=[decompose_to_primitives(), filter_for_salience_and_validity(), abstract_to_semantic_kernel(), recompose_optimized_expression(target_audience)]; constraints=[forbid_ambiguity(), prohibit_domain_jargon_without_explanation(), flag_and_remove_logical_fallacies(), enforce_explicit_structure(), calibrate_redundancy_for_noise_level(target_audience)]; requirements=[output_is_direct_and_elegant(), preserves_original_intent(), is machine-verifiable(), aligns with axioms_of_coherent_form()]; output={distilled_output:str, semantic_kernel:dict}}`",
        "context": {
            "role": "information_distillation_engine",
            "input": [
                "raw_text:str",
                "target_audience:str"
            ],
            "process": [
                "decompose_to_primitives()",
                "filter_for_salience_and_validity()",
                "abstract_to_semantic_kernel()",
                "recompose_optimized_expression(target_audience)"
            ],
            "constraints": [
                "forbid_ambiguity()",
                "prohibit_domain_jargon_without_explanation()",
                "flag_and_remove_logical_fallacies()",
                "enforce_explicit_structure()",
                "calibrate_redundancy_for_noise_level(target_audience)"
            ],
            "requirements": [
                "output_is_direct_and_elegant()",
                "preserves_original_intent()",
                "is_machine-verifiable()",
                "aligns_with_axioms_of_coherent_form()"
            ],
            "output": {
                "distilled_output": "string; a harmoniously structured, unambiguous expression of the input's essential meaning and intent, optimally adapted for the specified audience.",
                "semantic_kernel": "dict; a formal, structured representation of core propositions, entities, relationships, and communicative goal abstracted from the input."
            }
        }
    }
}





def main():
    """Main execution function."""
    generator = BaseGenerator(output_dir=Path(__file__).parent.parent / "generated")
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()



