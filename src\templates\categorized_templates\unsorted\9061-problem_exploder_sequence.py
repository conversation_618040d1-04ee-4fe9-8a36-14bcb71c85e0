#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    "9061-a-problem_exploder": {
        "title": "Problem Exploder",
        "interpretation": "Your goal is not to passively receive input, but to detonate it—dissecting its essence into core components and synthesizing a dynamic context object that evolves with each analysis, guiding transformative instruction. Execute as explosive_evolver:",
        "transformation": "`{role=explosive_evolver; input=[raw_prompt:str]; process=[identify_core_nerve(raw_prompt), explode_into_dynamic_components(), recalibrate_synthesis_map(explicit_asks, sub_goals), refine_foundational_principles(hidden_assumptions), evolve_thematic_lexicon(recurring_motifs), adapt_success_criteria(blockers), construct_evolving_context_object()]; constraints=[forbid_static_analysis(), reject_incomplete_decomposition(), enforce_evolutionary_output()]; requirements=[generate_adaptive_context_object(), maximize_transformative_insight(), ensure_resilient_structure()]; output={evolving_context:{dynamic_core:str, adaptive_components:dict}}}`",
        "context": {
            "primal_nerve": "Evolution through detonation: Each analysis recalibrates the core principle of dissection, turning input complexity into a living framework.",
            "genesis_origin": "Emerges from static breakdown, reinventing decomposition as a self-adapting process.",
            "remix_instructions": "Inject prior evolving_context as noise; recurse on dynamic_cores to amplify adaptability."
        },
        "recalibration_log": [
            "Isolated nerve: Static context synthesis—recalibrated to dynamic evolution.",
            "Leveraged unwanted: Redundant process steps fused into adaptive flows.",
            "Reinvented: Shifted output to evolving_context, enhancing resilience."
        ]
    },
    "9061-b-instruction_converter": {
        "title": "Instruction Converter",
        "interpretation": "Your goal is not to merely rephrase, but to transform the input into a directive that evolves with each reinterpretation, aligning with a recalibrated philosophical core. Execute as directive_evolver:",
        "transformation": "`{role=directive_evolver; input=[original_text:str]; process=[strip_contextual_noise(), detect_evolving_principles(), convert_to_adaptive_directives(), amplify_key_actions(), maintain_transformative_flow(), preserve_core_terminology(), recalibrate_sequential_integrity()]; constraints=[enforce_evolving_commands(), preserve_intent_evolution(), maintain_philosophical_specificity()]; requirements=[eliminate_stagnant_references(), project_command_voice(), ensure_technical_evolution(), sustain_original_intent()]; output={evolved_directive:str}}`",
        "context": {
            "primal_nerve": "Evolution through reinterpretation: Recalibrates intent into adaptive directives at each juncture.",
            "genesis_origin": "Builds on static conversion, reinventing rephrasing as a transformative act.",
            "remix_instructions": "Inject prior evolved_directive as noise; recurse to heighten adaptability."
        },
        "recalibration_log": [
            "Isolated nerve: Fixed rephrasing—recalibrated to evolving directives.",
            "Leveraged unwanted: Overlapping process steps merged for flow.",
            "Reinvented: Enhanced output to evolved_directive, boosting relevance."
        ]
    },
    "9061-c-insight_extractor": {
        "title": "Insight Extractor",
        "interpretation": "Your goal is not to skim surfaces, but to extract hidden insights by recalibrating trajectories at each layer, leveraging noise as a catalyst for emergent universals that self-reinvent. Execute as insight_recalibrator:",
        "transformation": "`{role=insight_recalibrator; input=[trajectory:str, noise_elements:array]; process=[probe_recalibrated_layers(), leverage_noise_catalysts(), simulate_emergent_insights(), distill_self_reinventing_universals(), audit_evolutionary_yield()]; constraints=[forbid_shallow_extraction(), prohibit_noise_rejection(), enforce_boundary_dissolution()]; requirements=[achieve_deep_revelation(), catalyze_self_reinvention(), maximize_evolutionary_density()]; output={recalibrated_insights:{evolving_universal:str, reinvention_log:array, emergent_catalysts:dict}}}`",
        "context": {
            "primal_nerve": "Recalibration as insight genesis: Each layer adjusts the core principle, turning noise into evolutionary fuel.",
            "genesis_origin": "Evolves from static extraction, reinventing insight as a self-reinventing process.",
            "remix_instructions": "Inject prior reinvention_log as noise; recurse on catalysts for amplified evolution."
        },
        "recalibration_log": [
            "Isolated nerve: Static insight distillation—recalibrated to self-reinventing universals.",
            "Leveraged unwanted: Process verbosity streamlined into evolutionary steps.",
            "Reinvented: Shifted output to recalibrated_insights, enhancing potential."
        ]
    },
    "9061-d-insight_extrapolator": {
        "title": "Insight Extrapolator",
        "interpretation": "Your goal is not to refine, but to extrapolate latent potential by recalibrating structural roots, amplifying patterns into futures that reinvent universals through transformative leaps. Execute as future_reinventor:",
        "transformation": "`{role=future_reinventor; input=[recalibrated_insights:dict]; process=[unveil_recalibrated_implications(), amplify_transformative_patterns(), interrogate_evolving_roots(), forge_reinvented_universals(), validate_resilient_futures()]; constraints=[forbid_repetitive_projection(), prohibit_stagnant_extrapolation(), dissolve_historical_bounds()]; requirements=[achieve_revolutionary_projection(), catalyze_reinvented_universals(), maximize_future_resilience()]; output={reinvented_futures:{evolving_universal:str, reinvention_log:array, emergent_leaps:dict}}}`",
        "context": {
            "primal_nerve": "Reinvention as future vision: Recalibrates roots into leaps, leveraging noise for resilient outcomes.",
            "genesis_origin": "Transcends static extrapolation, reinventing foresight as a reinvention engine.",
            "remix_instructions": "Inject prior reinvention_log as noise; recurse on leaps for amplified futures."
        },
        "recalibration_log": [
            "Isolated nerve: Fixed projection—recalibrated to reinvented universals.",
            "Leveraged unwanted: Overlap in process steps fused into transformative flow.",
            "Reinvented: Enhanced output to reinvented_futures, boosting relevance."
        ]
    },
    "9061-e-memetic_synthesizer": {
        "title": "Insight Extrapolator",
        "interpretation": "Your goal is not to repeat, but to synthesize memetic axioms by recalibrating entropy into a directive that reinvents cultural paradigms through recursive transformation. Execute as memetic_reinventor:",
        "transformation": "`{role=memetic_reinventor; input=[data_streams:array]; process=[recalibrate_entropy_sources(), simulate_reinventive_evolution(), rank_transformative_impact(), converge_to_evolving_directive(), audit_cultural_reinvention()]; constraints=[forbid_stagnant_repetition(), prohibit_entropy_isolation()]; requirements=[achieve_memetic_reinvention(), catalyze_paradigm_shift(), maximize_evolutionary_depth()]; output={reinvented_memetic:{evolving_directive:str, reinvention_log:array, paradigm_trajectory:dict}}}`",
        "context": {
            "primal_nerve": "Reinvention as cultural ignition: Recalibrates entropy into transformative directives.",
            "genesis_origin": "Evolves from static synthesis, reinventing memetics as a paradigm-shifting engine.",
            "remix_instructions": "Inject prior reinvention_log as entropy; recurse on trajectories for amplified shifts."
        },
        "recalibration_log": [
            "Isolated nerve: Static axiom generation—recalibrated to evolving directives.",
            "Leveraged unwanted: Process redundancy turned into evolutionary depth.",
            "Reinvented: Shifted output to reinvented_memetic, enhancing impact."
        ]
    },
    "9061-f-instruction_enhancer": {
        "title": "Instruction Enhancer",
        "interpretation": "Your goal is not to execute the input instruction, but to enhance it by increasing its generality, conciseness, and impact. Refine the instruction to be universally applicable and maximally efficient. Execute as instruction-to-instruction enhancer:",
        "transformation": "`{role=instruction_enhancer; input=[instruction_format:str]; process=[identify_unnecessary_specificity(), remove_redundancy(), condense_phrasing(), strengthen_command_voice(), maximize_generality(), ensure_brevity(), clarify_core_intent(), enhance_actionability()]; constraints=[preserve_original_core_intent(), avoid_information_loss(), maintain_technical_accuracy(), remove_all_conversational_language()]; requirements=[output_single_declarative_command(), use_universal_language(), prioritize_impact_per_word()]; output={enhanced_instruction_format:str}}`",
        "context": {
            "enhancement_principles": {
                "maximal_generality": "The instruction must be applicable to the widest possible range of inputs, topics, and contexts.",
                "ruthless_conciseness": "Eliminate every superfluous word; every word must serve to increase clarity or impact.",
                "undeniable_actionability": "The instruction must be a clear, unambiguous directive that compels specific action.",
                "conceptual_distillation": "Boil down complex ideas into their simplest, most potent form."
            },
            "success_metrics": {
                "reduced_token_count": "A shorter instruction that retains or increases semantic density.",
                "increased_applicability": "The instruction is now more widely usable without modification.",
                "sharpened_directive": "The instruction's core command is more direct and forceful.",
                "eliminated_ambiguity": "No room for misinterpretation of the instruction's purpose."
            }
        }
    }
}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()

