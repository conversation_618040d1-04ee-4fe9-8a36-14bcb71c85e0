# Technology Stack

## Core Framework
- **Python 3.9+** - Primary runtime environment
- **LiteLLM** - Multi-provider LLM abstraction layer
- **Pydantic** - Data validation and structured configuration
- **AsyncIO** - Asynchronous execution engine

## LLM Integration
- **OpenAI** - GPT models (gpt-4.1, gpt-4o, etc.)
- **Anthropic** - Claude models (claude-3.7-sonnet, etc.)
- **Google** - Gemini models (gemini-2.5-pro, etc.)
- **DeepSeek** - DeepSeek models (deepseek-chat, etc.)

## Data Processing
- **JSON** - Template catalog storage and execution results
- **Markdown** - Template definition format
- **YAML** - Configuration management
- **Jinja2** - Template rendering engine

## Development Tools
- **Rich** - Enhanced terminal output
- **TQDM** - Progress indicators
- **Loguru** - Advanced logging
- **JSONSchema** - Data validation

## Architecture Pattern
- **Template-driven instruction sequences**
- **Modular processor system**
- **Catalog-based template management**
- **Chain execution with context preservation**
